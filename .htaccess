RewriteEngine On

# Set the base directory
RewriteBase /

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove www if present
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1%{REQUEST_URI} [R=301,L]

# Prevent access to internal server paths
RewriteCond %{THE_REQUEST} /home/<USER>
RewriteCond %{THE_REQUEST} /czjdqoab/ [NC]
RewriteRule .* - [F,L]

# Handle success stories path specifically
RewriteCond %{REQUEST_URI} ^/pages/insights/success-stories/?$ [NC]
RewriteRule .* /pages/insights/success-stories.html [L]

# Redirect .html requests to clean URLs
RewriteCond %{THE_REQUEST} ^[A-Z]{3,9}\ /([^\s\?]+)\.html[\s\?] [NC]
RewriteRule ^ /%1 [R=301,L]

# Internally rewrite clean URLs to .html files
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^(.*)$ $1.html [NC,L]

# Prevent directory listing
Options -Indexes

# The trailing slash rule is removed for now to simplify
# Apache usually handles directory redirects automatically
