/* UX/UI Design Page Styles */
:root {
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-purple: #7C4DFF;
    --light-purple: #B388FF;
    --white: #FFFFFF;
    --off-white: #F4F7FA;
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;
    --shadow-purple: rgba(124, 77, 255, 0.3);
    
    /* Shadows */
    --shadow-sm: 0 4px 6px rgba(124, 77, 255, 0.1);
    --shadow-md: 0 8px 15px rgba(124, 77, 255, 0.15);
    --shadow-lg: 0 15px 30px rgba(124, 77, 255, 0.2);
    
    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 2rem;
    --space-lg: 4rem;
    --space-xl: 6rem;
    
    /* Typography */
    --font-size-xs: 0.875rem;
    --font-size-sm: 1rem;
    --font-size-md: 1.25rem;
    --font-size-lg: 1.5rem;
    --font-size-xl: 2rem;
    --font-size-2xl: 2.5rem;
    --font-size-3xl: 3.5rem;
}

/* Base Styles */
.ux-content {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-navy);
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
}

.ux-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 2;
}

/* Hero Section */
.ux-hero {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
    position: relative;
    overflow: hidden;
}

.ux-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(124, 77, 255, 0.1), transparent 40%);
    z-index: 1;
}

.ux-hero-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.ux-hero-content {
    position: relative;
    z-index: 2;
}

.ux-hero-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--space-md);
    line-height: 1.2;
    color: var(--white);
}

.ux-hero-subtitle {
    font-size: var(--font-size-md);
    margin-bottom: var(--space-lg);
    color: var(--text-light);
}

.ux-hero-image-container {
    position: relative;
    z-index: 2;
}

.ux-hero-image {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    transition: transform 0.5s ease;
}

.ux-hero-image:hover {
    transform: translateY(-10px);
}

/* Button Styles */
.ux-btn {
    display: inline-block;
    padding: 12px 30px;
    background: var(--accent-purple);
    color: var(--white);
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.ux-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent-purple), var(--light-purple));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.ux-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.ux-btn:hover::before {
    opacity: 1;
}

.ux-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.ux-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top-color: var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Overview Section */
.ux-overview {
    padding: var(--space-xl) 0;
    text-align: center;
    position: relative;
}

.ux-overview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 80% 20%, rgba(124, 77, 255, 0.05), transparent 40%);
    z-index: 1;
}

.ux-section-heading {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--space-sm);
    color: var(--white);
    position: relative;
    padding-bottom: var(--space-sm);
}

.ux-section-heading::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-purple), var(--light-purple));
    border-radius: 3px;
}

.ux-section-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto var(--space-lg);
    color: var(--text-muted);
    font-size: var(--font-size-md);
}

/* Stats Section */
.ux-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    position: relative;
    z-index: 2;
}

.ux-stat-item {
    background: var(--secondary-navy);
    padding: var(--space-md);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    text-align: center;
    border-bottom: 3px solid var(--accent-purple);
    position: relative;
    overflow: hidden;
}

.ux-stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.ux-stat-item::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(124, 77, 255, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
}

.ux-stat-item:hover::after {
    opacity: 1;
}

.ux-stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--accent-purple);
    margin-bottom: var(--space-xs);
    position: relative;
    z-index: 1;
}

.ux-stat-label {
    color: var(--text-light);
    font-weight: 500;
    position: relative;
    z-index: 1;
}

/* Capabilities Section */
.ux-capabilities {
    padding: var(--space-xl) 0;
    background: var(--secondary-navy);
    position: relative;
}

.ux-capabilities::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 40%, rgba(124, 77, 255, 0.05), transparent 40%);
    z-index: 1;
}

.ux-capabilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-md);
    position: relative;
    z-index: 2;
}

.ux-capability-item {
    background: var(--deep-navy);
    padding: var(--space-md);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border-top: 4px solid var(--accent-purple);
    position: relative;
    overflow: hidden;
}

.ux-capability-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.ux-capability-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(124, 77, 255, 0.05) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
}

.ux-capability-item:hover::after {
    opacity: 1;
}

.ux-capability-icon {
    width: 48px;
    height: 48px;
    margin-bottom: var(--space-sm);
    color: var(--accent-purple);
    position: relative;
    z-index: 1;
}

.ux-capability-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    color: var(--white);
    position: relative;
    z-index: 1;
}

.ux-capability-description {
    color: var(--text-muted);
    position: relative;
    z-index: 1;
}

/* Process Section */
.ux-process {
    padding: var(--space-xl) 0;
    background: var(--primary-navy);
    position: relative;
}

.ux-process::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(124, 77, 255, 0.05), transparent 50%);
    z-index: 1;
}

.ux-process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-md);
    position: relative;
    z-index: 2;
}

.ux-process-step {
    background: var(--secondary-navy);
    padding: var(--space-md);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-left: 4px solid var(--accent-purple);
}

.ux-process-step:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.ux-process-step::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(124, 77, 255, 0.05) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
}

.ux-process-step:hover::after {
    opacity: 1;
}

.ux-process-step-number {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--accent-purple);
    opacity: 0.5;
    margin-bottom: var(--space-sm);
    position: relative;
    z-index: 1;
}

.ux-process-step-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    color: var(--white);
    position: relative;
    z-index: 1;
}

.ux-process-step-description {
    color: var(--text-muted);
    position: relative;
    z-index: 1;
}

/* Case Studies Section */
.ux-case-studies {
    padding: var(--space-xl) 0;
    background: var(--primary-navy);
    position: relative;
}

.ux-case-studies::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 60%, rgba(124, 77, 255, 0.05), transparent 40%);
    z-index: 1;
}

.ux-case-studies-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-md);
    position: relative;
    z-index: 2;
}

.ux-case-study-item {
    background: var(--secondary-navy);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
}

.ux-case-study-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.case-study-content-wrapper {
    padding: var(--space-md);
}

.case-study-text {
    position: relative;
    z-index: 1;
}

.case-study-step-number {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--accent-purple);
    opacity: 0.5;
    margin-bottom: var(--space-sm);
    display: block;
}

.case-study-text h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    color: var(--white);
}

.case-study-text p {
    color: var(--text-muted);
    margin-bottom: var(--space-md);
}

.case-study-button {
    background: var(--accent-purple);
    color: var(--white);
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.case-study-button:hover {
    background: var(--light-purple);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

/* Technologies Section */
.ux-technologies {
    padding: var(--space-xl) 0;
    background: var(--secondary-navy);
    position: relative;
}

.ux-technologies::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 40% 30%, rgba(124, 77, 255, 0.05), transparent 40%);
    z-index: 1;
}

.ux-tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    position: relative;
    z-index: 2;
}

.ux-tech-item {
    background: var(--deep-navy);
    padding: var(--space-md);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.ux-tech-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.ux-tech-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto var(--space-sm);
    color: var(--accent-purple);
    transition: all 0.3s ease;
}

.ux-tech-item:hover .ux-tech-icon {
    transform: scale(1.1);
}

.ux-tech-item p {
    color: var(--text-light);
    font-weight: 500;
}

/* CTA Section */
.ux-cta {
    padding: var(--space-xl) 0;
    background: var(--primary-navy);
    position: relative;
    text-align: center;
}

.ux-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(124, 77, 255, 0.1), transparent 60%);
    z-index: 1;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(4, 16, 38, 0.8);
    z-index: 1000;
    overflow-y: auto;
}

.modal-content {
    position: relative;
    background: var(--secondary-navy);
    margin: 5% auto;
    padding: var(--space-lg);
    width: 90%;
    max-width: 800px;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    color: var(--text-light);
}

.close-modal {
    position: absolute;
    top: var(--space-sm);
    right: var(--space-sm);
    font-size: 2rem;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-muted);
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--white);
}

.case-study-details {
    margin-top: var(--space-md);
}

.case-study-description {
    margin-bottom: var(--space-md);
    color: var(--text-muted);
}

.case-study-results h3,
.case-study-technologies h3 {
    color: var(--white);
    margin-bottom: var(--space-sm);
    font-size: var(--font-size-lg);
}

.case-study-results ul {
    list-style-type: none;
    padding-left: 0;
    margin-bottom: var(--space-md);
}

.case-study-results li {
    margin-bottom: var(--space-xs);
    padding-left: 20px;
    position: relative;
}

.case-study-results li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-purple);
}

.tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-xs);
}

.tech-tag {
    background: var(--deep-navy);
    color: var(--accent-purple);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: var(--font-size-xs);
    border: 1px solid var(--accent-purple);
}

/* Animation Classes */
.scroll-reveal {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

.floating-element {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .ux-hero-wrapper {
        gap: 40px;
    }
    
    .ux-hero-title {
        font-size: var(--font-size-2xl);
    }
}

@media (max-width: 992px) {
    .ux-container {
        padding: 0 30px;
    }
    
    .ux-capabilities-grid,
    .ux-tech-grid,
    .ux-process-steps {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .ux-hero {
        padding: 60px 0;
    }
    
    .ux-hero-wrapper {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .ux-hero-title {
        font-size: var(--font-size-xl);
    }
    
    .ux-hero-image-container {
        order: -1;
    }
    
    .ux-capabilities-grid,
    .ux-stats-grid,
    .ux-case-studies-list,
    .ux-tech-grid,
    .ux-process-steps {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .ux-container {
        padding: 0 20px;
    }
    
    .ux-hero-title {
        font-size: var(--font-size-lg);
    }
    
    .ux-section-heading {
        font-size: var(--font-size-xl);
    }
    
    .ux-btn {
        padding: 10px 20px;
    }
}