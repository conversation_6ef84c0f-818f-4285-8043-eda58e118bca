/* Contact Page Styles */

/* Main Container */
.contact-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 60px 20px;
}

.page-title {
    font-size: 32px;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 40px;
    text-align: center;
}

/* Contact Form Container */
.contact-form-container {
    display: flex;
    background: linear-gradient(90deg, #061035 0%, #0A1D45 100%);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Contact Information Section */
.contact-info {
    background-color: #000;
    color: #fff;
    padding: 40px;
    width: 40%;
    position: relative;
    overflow: hidden;
}

.contact-info h2 {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 10px;
}

.contact-tagline {
    color: #E7E7E7;
    margin-bottom: 40px;
}

.contact-details {
    margin-top: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
}

.contact-icon {
    width: 24px;
    height: 24px;
    margin-right: 15px;
}

.contact-item a {
    color: #E7E7E7;
    text-decoration: none;
    transition: color 0.3s;
}

.contact-item a:hover {
    color: #ffffff;
}

.address p {
    margin: 0 0 5px 0;
    color: #E7E7E7;
}

/* Decorative Circles */
.circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.circle-large {
    width: 200px;
    height: 200px;
    bottom: -100px;
    right: -50px;
}

.circle-small {
    width: 100px;
    height: 100px;
    bottom: 20px;
    right: 50px;
}

/* Contact Form Section */
.contact-form {
    padding: 40px;
    width: 60%;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
}

.form-group.full-width {
    width: 100%;
}

label {
    display: block;
    margin-bottom: 8px;
    color: #C7E5FF;
    font-size: 14px;
}

input, textarea {
    width: 100%;
    padding: 12px;
    background-color: transparent;
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
    font-size: 16px;
    transition: border-color 0.3s;
}

input:focus, textarea:focus {
    outline: none;
    border-color: #ffffff;
}

textarea {
    min-height: 120px;
    resize: vertical;
}

.send-button {
    background-color: #000;
    color: #fff;
    border: none;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 20px;
    transition: background-color 0.3s;
}

.send-button:hover {
    background-color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-form-container {
        flex-direction: column;
    }
    
    .contact-info, .contact-form {
        width: 100%;
    }
    
    .form-row {
        flex-direction: column;
        gap: 20px;
    }
}
