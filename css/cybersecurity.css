/* Cybersecurity Services Page Styles */
@import url('services-base.css');

:root {
    /* Custom colors for cybersecurity */
    --cyber-primary: #061035;
    --cyber-secondary: #0A1D45;
    --cyber-accent: #00B0FF;
    --cyber-accent-alt: #FF3D00;
    --cyber-light: #E6F7FF;
    --cyber-dark: #041026;
    --cyber-gradient: linear-gradient(135deg, var(--cyber-primary), var(--cyber-secondary));
    --glow-white: 0 0 10px rgba(255, 255, 255, 0.7), 0 0 20px rgba(255, 255, 255, 0.5), 0 0 30px rgba(255, 255, 255, 0.3);
}

/* Override hero section styles */
.service-hero {
    background-color: var(--cyber-primary); /* Changed from gradient to solid navy */
    min-height: 40vh;
    position: relative;
    overflow: hidden;
    padding: 80px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Removed ::before pseudo-element that added the noisy pattern */
.service-hero__content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    text-align: center;
}

.service-hero__title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #FFFFFF;
    text-shadow: var(--glow-white);
    display: inline-block;
    animation: pulse-glow 2s infinite alternate;
}

.service-hero__subtitle {
    font-size: 1.25rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* Custom styles for cybersecurity services */
.security-icon {
    fill: var(--cyber-accent);
    transition: fill 0.3s ease;
}

.service-tech-item:hover .security-icon {
    fill: var(--cyber-accent-alt);
}

.security-alert-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: var(--cyber-accent-alt);
    color: var(--white);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(255, 61, 0, 0.3);
}

/* Service cards redesign */
.service-card {
    background: linear-gradient(135deg, rgba(10, 29, 69, 0.9), rgba(6, 16, 53, 0.9));
    border-left: 4px solid var(--cyber-accent);
    border-radius: 8px;
    padding: 30px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/cyber-pattern.svg');
    opacity: 0.02;
    z-index: 0;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    border-left: 4px solid var(--cyber-accent-alt);
}

.service-card__content {
    position: relative;
    z-index: 1;
}

/* Added styles for card text */
.service-card__title {
    color: #FFFFFF; /* Set title color to white */
    margin-bottom: 0.75rem; /* Add spacing below title */
}

.service-card__description {
    color: #E0E0E0; /* Set description color to light grey/off-white */
    line-height: 1.6; /* Improve readability */
}

/* Added style for overview paragraph text */
.service-overview p {
    color: #FFFFFF; /* Set overview paragraph text to white */
}

/* Stats section redesign */
.service-stats-container {
    background: var(--cyber-gradient);
    border-radius: 12px;
    padding: 40px;
    margin: 60px 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 30px;
    position: relative;
    overflow: hidden;
}

.service-stats-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/cyber-pattern.svg');
    opacity: 0.05;
    z-index: 0;
}

.service-stat__item {
    position: relative;
    z-index: 1;
    text-align: center;
    flex: 1;
    min-width: 150px;
}

.service-stat__number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--cyber-accent);
    margin-bottom: 10px;
}

.service-stat__label {
    font-size: 1rem;
    color: var(--cyber-primary); /* Changed stat label color to navy */
    font-weight: 500;
}

/* Glowing headings for all sections */
.service-overview h2,

.service-case-study__title {
    color: #431bd3;
    text-shadow: var(--glow-white);
    animation: pulse-glow 2s infinite alternate;
}

/* Animation for glowing effect */
@keyframes pulse-glow {
    0% {
        text-shadow: 0 0 5px rgba(255, 255, 255, 0.7),
                   0 0 10px rgba(255, 255, 255, 0.5);
    }
    100% {
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.9),
                   0 0 20px rgba(255, 255, 255, 0.7),
                   0 0 30px rgba(255, 255, 255, 0.5),
                   0 0 40px rgba(0, 176, 255, 0.3);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .service-hero__title {
        font-size: 2.5rem;
    }

    .service-hero__subtitle {
        font-size: 1rem;
    }

    .service-stats-container {
        padding: 30px 20px;
    }

    .service-stat__number {
        font-size: 2.5rem;
    }
}