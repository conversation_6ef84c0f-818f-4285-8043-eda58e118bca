/* Banking Page Comprehensive Styling */
:root {
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-blue: #00B0FF;
    --light-blue: #C7E5FF;
    --white: #FFFFFF;
    --off-white: #F4F7FA;
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;
    --shadow-blue: rgba(0, 176, 255, 0.3); /* Added for button shadows */
}

/* Global Resets & Base Styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-navy);
    color: var(--text-light);
    line-height: 1.6;
    margin: 0; /* Reset default body margin */
    overflow-x: hidden; /* Prevent horizontal scroll */
}

/* Hero Section */
.banking-hero {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
    position: relative;
    overflow: hidden;
}

.banking-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(0, 176, 255, 0.1), transparent 40%);
    z-index: 1;
}

.banking-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 2;
}

/* Hero Wrapper for Side-by-Side Layout */
.banking-hero-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 50px;
}

.banking-hero-content {
    flex: 1;
    max-width: 50%;
    color: var(--white);
}

.banking-hero-content h1 {
    font-size: 52px;
    margin-bottom: 20px;
    font-weight: 800;
    color: var(--white);
    background: linear-gradient(45deg, var(--white), var(--light-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.banking-hero-content p {
    font-size: 18px;
    line-height: 1.7;
    margin-bottom: 30px;
    color: var(--text-muted);
}

.banking-hero-btn {
    background-color: var(--accent-blue);
    color: var(--deep-navy);
    padding: 14px 35px;
    border: none;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 20px rgba(0, 176, 255, 0.3);
    text-decoration: none; /* Add if it's an <a> tag */
    display: inline-block; /* Ensures padding and margins work */
}

.banking-hero-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 25px rgba(0, 176, 255, 0.4);
}

.banking-hero-image-container {
    flex: 1;
    max-width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.banking-hero-image {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: block; /* Prevents potential extra space below image */
}

/* Capabilities Section */
.banking-capabilities {
    background-color: var(--deep-navy);
    padding: 80px 0;
}

.banking-section-heading {
    font-size: 40px;
    margin-bottom: 20px;
    text-align: center;
    color: var(--white);
    font-weight: 700;
}

.banking-section-description {
    font-size: 18px;
    color: var(--text-muted);
    text-align: center;
    max-width: 800px;
    margin: 0 auto 60px; /* Increased bottom margin */
}

.banking-capabilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* More responsive grid */
    gap: 30px;
}

.banking-capability-item {
    background: rgb(255, 255, 255);
    border: 1px solid rgba(0, 176, 255, 0.2);
    padding: 35px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
    display: flex; /* Use flex for content alignment */
    flex-direction: column; /* Stack content vertically */
    justify-content: center; /* Center content vertically */
    min-height: 200px; /* Give items a minimum height */
}

.banking-capability-item:hover {
    transform: translateY(-10px);
    background: rgba(10, 29, 69, 0.7);
    border-color: var(--accent-blue);
}

.banking-capability-item:hover h3 {
    color: var(--white);
}

.banking-capability-item:hover p {
    color: var(--white);
}

.banking-capability-item h3 {
    color: var(--accent-blue);
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 600;
    margin-top: 0; /* Remove default top margin */
}

.banking-capability-item p {
    color: var(--deep-navy);
    font-size: 16px;
    line-height: 1.6; /* Ensure consistent line height */
}

/* Challenges Section */
.banking-challenges {
    padding: 80px 0;
    background-color: var(--secondary-navy);
}

.challenges-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    padding: 40px;
    border-radius: 15px;
    background: rgba(10, 29, 69, 0.6);
    border: 1px solid rgba(0, 176, 255, 0.2);
    backdrop-filter: blur(10px);
}

.challenges-content h2 {
    font-size: 36px;
    color: var(--white);
    margin-bottom: 20px;
    font-weight: 700;
}

.challenges-content p {
    font-size: 18px;
    color: var(--text-muted);
    line-height: 1.7;
}


/* =================================== */
/* ===== CASE STUDIES SECTION ======== */
/* =================================== */

.banking-case-studies {
    padding: 80px 0; /* Add padding to the section */
}

.banking-case-studies-list {
    display: grid;
    grid-template-columns: 1fr; /* Single column layout */
    gap: 50px; /* Increased spacing between case studies */
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px; /* Padding for smaller screens */
}

.banking-case-study-item {
    display: flex;
    flex-direction: column; /* Stack image and text vertically */
    background: rgba(26, 44, 79, 0.6);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s ease;
    border: 1px solid rgba(0, 176, 255, 0.2);
    backdrop-filter: blur(10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.banking-case-study-item {
    background: var(--white);
}

.banking-case-study-item:hover {
    transform: scale(1.02); /* Slightly more subtle hover scale */
    box-shadow: 0 20px 40px rgba(0, 176, 255, 0.25); /* Adjusted shadow */
}

.case-study-content-wrapper {
    display: flex;
    flex-direction: column; /* Ensures stacking */
    width: 100%;
    height: 100%;
}


.case-study-image-container {
    width: 100%;
    overflow: hidden;
}

.case-study-image {
    width: 100%;
    height: 250px; /* Maintain fixed height for consistency */
    object-fit: cover;
    transition: transform 0.4s ease;
    display: block; /* Removes extra space below image */
    border-radius: 20px 20px 0 0; /* Match top corners */
}


/* === Updated Case Study Text Styling (START) === */

.case-study-text {
    flex: 1; /* Takes remaining vertical space */
    padding: 35px 30px 30px 30px; /* Adjusted padding: More top, standard sides/bottom */
    display: flex;
    flex-direction: column;
    position: relative; /* Keep for step number positioning */
    color: var(--primary-navy); /* Default text color for the container */
    border-radius: 20px; /* Add border radius since we removed the image container */
}

.case-study-step-number {
    position: absolute;
    top: 30px; /* Adjusted position */
    right: 30px; /* Adjusted position */
    opacity:
    10; /* More subtle */
    font-size: 2.2rem; /* Slightly smaller */
    font-weight: 800; /* Bolder */
color:#00B0FF;    opacity: 0.2; /* More subtle */
    line-height: 1; /* Prevent extra vertical space */
    z-index: 0; /* Ensure it's behind text if overlap occurs */
    user-select: none; /* Prevent text selection */
}

.case-study-text h3 {
    color: var(--primary-navy); /* Keep bright white for title */
    font-size: 2rem; /* Slightly larger for more impact */
    font-weight: 700; /* Ensure strong weight */
    margin-bottom: 15px; /* Space below title */
    margin-top: 0;
    line-height: 1.3; /* Tighter line height for multi-line titles */
    letter-spacing: 0.5px; /* Subtle spacing for refinement */
    position: relative; /* Ensure it stays above the step number */
    z-index: 1;
}

.case-study-text p {
    color: var(--primary-navy); /* Use muted color for description */
    font-size: 1rem; /* Standard readable size (16px) */
    line-height: 1.7; /* Generous line height for readability */
    margin-bottom: 30px; /* Increased space before the button */
    flex-grow: 1; /* Allows paragraph to push button down */
    position: relative; /* Ensure it stays above the step number */
    z-index: 1;
}

.case-study-button {
    align-self: flex-start; /* Align button to the left */
    margin-top: auto; /* Pushes the button to the bottom */
    background-color: var(--accent-blue);
    color: var(--deep-navy);
    padding: 12px 25px;
    border-radius: 8px;
    border: none; /* Ensure no default border */
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 176, 255, 0.2); /* Softer initial shadow */
    cursor: pointer; /* Ensure cursor changes */
    position: relative; /* Ensure it stays above the step number */
    z-index: 1;
}

.case-study-button:hover {
    transform: translateY(-3px); /* Slightly less jump */
    background-color: var(--light-blue);
    box-shadow: 0 8px 20px var(--shadow-blue); /* Slightly enhance hover shadow */
}

/* Ensure hidden content doesn't affect layout */
.case-study-hidden-content {
    display: none;
}

/* === Updated Case Study Text Styling (END) === */


/* =================================== */
/* ========= MODAL STYLES ============ */
/* =================================== */

.modal {
    display: none; /* Hidden by default */
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0, 0, 0, 0.7); /* Darker overlay */
    /* Use flexbox to center the modal content */
    display: flex; /* This needs to be set by JS to 'flex' when opening */
    justify-content: center;
    align-items: center;
    opacity: 0; /* Start fully transparent */
    transition: opacity 0.3s ease-in-out; /* Fade effect */
    padding: 20px; /* Padding around modal box */
}

/* Add class 'modal-visible' via JS to show */
.modal.modal-visible {
    opacity: 1;
}

.modal-content {
    background: var(--secondary-navy); /* Use secondary navy for modal bg */
    color: var(--text-light);
    padding: 40px; /* More padding */
    border-radius: 15px;
    border: 1px solid rgba(0, 176, 255, 0.2);
    width: 100%; /* Responsive width */
    max-width: 700px; /* Max width */
    position: relative;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5); /* Stronger shadow */
    backdrop-filter: blur(8px); /* Slightly less blur inside modal */
    transform: scale(0.95); /* Start slightly smaller */
    transition: transform 0.3s ease-in-out; /* Scale effect */
}

.modal.modal-visible .modal-content {
    transform: scale(1); /* Scale to full size when visible */
}

.modal-content h4 {
    color: var(--accent-blue);
    font-size: 1.6rem; /* Slightly larger H4 */
    margin-bottom: 15px;
    font-weight: 700;
    border-bottom: 1px solid rgba(0, 176, 255, 0.2); /* Separator line */
    padding-bottom: 10px;
}

.modal-content p {
    font-size: 1rem; /* Standard text size */
    line-height: 1.7;
    margin-bottom: 25px;
    color: var(--text-muted); /* Use muted text for paragraphs */
}

.modal-content p:last-child {
    margin-bottom: 0; /* Remove margin from last paragraph */
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: var(--text-muted); /* Muted color for close */
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease, transform 0.2s ease;
    line-height: 1; /* Ensure consistent positioning */
}

.close:hover {
    color: var(--accent-blue);
    transform: rotate(90deg); /* Add rotation on hover */
}


/* =================================== */
/* ========== CTA SECTION ============ */
/* =================================== */

.banking-cta {
    background: linear-gradient(135deg, var(--secondary-navy), var(--deep-navy));
    padding: 100px 0; /* Increase padding */
    text-align: center;
    border-top: 1px solid rgba(0, 176, 255, 0.1);
    position: relative; /* For potential pseudo-elements */
    overflow: hidden;
}

.banking-cta .cta-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 60px 40px; /* Adjust padding */
    background: rgba(10, 29, 69, 0.7);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(12px);
    position: relative; /* Ensure content is above pseudo-elements */
    z-index: 2;
}

.banking-cta h2 {
    font-size: 44px;
    color: var(--white);
    margin-bottom: 20px;
    font-weight: 700;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
    line-height: 1.3;
}

.banking-cta p {
    font-size: 20px;
    color: var(--text-muted);
    margin-bottom: 40px;
    line-height: 1.8;
    max-width: 600px; /* Limit width for readability */
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    background-color: var(--accent-blue);
    color: var(--deep-navy);
    padding: 16px 40px;
    border: none;
    border-radius: 10px;
    font-size: 20px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 12px 24px rgba(0, 176, 255, 0.4);
    text-decoration: none;
    display: inline-block;

}

.cta-button:hover {
    transform: translateY(-7px) scale(1.05);
    box-shadow: 0 18px 35px rgba(0, 176, 255, 0.5);
    background-color: var(--light-blue);
    color: var(--deep-navy);
}


.cta-button-animated {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 12px 24px rgba(0, 176, 255, 0.4);
    }
    50% {
        transform: scale(1.05); /* Slightly less intense pulse */
        box-shadow: 0 16px 30px rgba(0, 176, 255, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 12px 24px rgba(0, 176, 255, 0.4);
    }
}

/* =================================== */
/* ======== RESPONSIVE DESIGN ======== */
/* =================================== */

@media (max-width: 1024px) {
    .banking-container {
        padding: 0 30px;
    }
    .banking-hero-content h1 {
        font-size: 48px;
    }
}

@media (max-width: 900px) {
    .banking-hero-wrapper {
        flex-direction: column;
        text-align: center;
        gap: 40px;
    }

    .banking-hero-content,
    .banking-hero-image-container {
        max-width: 100%;
        flex-basis: auto; /* Reset flex basis */
    }

    .banking-hero-image-container {
        margin-top: 0;
    }

    .banking-hero-btn {
        margin: 0 auto; /* Center button */
    }


    .banking-section-heading {
        font-size: 36px;
    }

    .banking-section-description {
        font-size: 17px;
        max-width: 90%;
    }

    .challenges-content {
        padding: 30px;
    }

    .challenges-content h2 {
        font-size: 32px;
    }

    .challenges-content p {
        font-size: 17px;
    }

    .banking-cta h2 {
        font-size: 38px;
    }

    .banking-cta p {
        font-size: 18px;
    }
}

@media (max-width: 768px) {
    body {
        line-height: 1.5; /* Adjust base line-height */
    }

    .banking-container {
        padding: 0 20px;
    }

    .banking-hero {
        padding: 60px 0;
    }

    .banking-hero-content h1 {
        font-size: 40px;
    }

    .banking-hero-content p {
        font-size: 16px;
    }

    .banking-capabilities,
    .banking-challenges,
    .banking-case-studies,
    .banking-cta {
        padding: 60px 0;
    }

     .banking-capabilities-grid {
        gap: 20px;
    }

    .banking-capability-item {
        padding: 30px 25px; /* Adjust padding */
        min-height: 180px;
    }

    .banking-capability-item h3 {
         font-size: 22px;
    }

    .banking-section-heading {
        font-size: 32px;
    }

    .banking-case-studies-list {
        gap: 40px;
    }

    .case-study-text h3 {
        font-size: 1.8rem;
    }
    .case-study-text p {
        font-size: 0.95rem; /* Slightly smaller on mobile */
    }

    .modal-content {
        padding: 30px 20px; /* Less horizontal padding */
        max-width: 95%;
    }

    .modal-content h4 {
        font-size: 1.4rem;
    }
     .modal-content p {
        font-size: 0.95rem;
    }

    .banking-cta .cta-content {
        padding: 40px 25px;
    }

    .banking-cta h2 {
        font-size: 32px;
    }

    .banking-cta p {
        font-size: 16px;
    }

    .cta-button {
        font-size: 18px;
        padding: 14px 35px;
    }
}

@media (max-width: 480px) {
    .banking-hero-content h1 {
        font-size: 32px;
        line-height: 1.2;
    }

    .banking-hero-content p {
        font-size: 15px;
    }

    .banking-hero-btn {
        font-size: 16px;
        padding: 12px 30px;
    }

    .banking-section-heading {
        font-size: 28px;
    }
    .banking-section-description {
        font-size: 15px;
    }

    .banking-capability-item h3 {
        font-size: 20px;
    }

    .banking-capability-item p {
        font-size: 14px;
    }

    .case-study-image {
        height: 200px; /* Reduce image height */
    }

    .case-study-text {
        padding: 25px 20px 20px 20px;
    }

    .case-study-text h3 {
        font-size: 1.5rem;
    }

    .case-study-text p {
        font-size: 0.9rem;
        margin-bottom: 25px;
    }
    .case-study-step-number {
        font-size: 1.8rem;
        top: 20px;
        right: 20px;
    }
    .case-study-button {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .challenges-content h2 {
        font-size: 28px;
    }
     .challenges-content p {
        font-size: 15px;
    }


    .banking-cta h2 {
        font-size: 28px;
    }

    .banking-cta p {
        font-size: 15px;
    }
    .cta-button {
        font-size: 16px;
        padding: 12px 30px;
    }

    .modal-content {
        padding: 25px 15px;
    }
     .modal-content h4 {
        font-size: 1.3rem;
    }
     .modal-content p {
        font-size: 0.9rem;
    }
    .close {
        font-size: 24px;
        top: 10px;
        right: 15px;
    }
}

/* Additions for Scroll Reveal Animation (Make sure keyframes are defined) */
.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
}

/* Define the slideUp animation if it's not elsewhere */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Additions for other effects if not defined elsewhere */
.floating-element {
    /* Example floating animation */
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
    100% { transform: translateY(0px); }
}

.dynamic-shadow {
    animation: dynamicShadow 6s ease-in-out infinite;
}

@keyframes dynamicShadow {
     0% { box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3); }
     50% { box-shadow: 0 30px 50px rgba(0, 0, 0, 0.4); }
     100% { box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3); }
}

.button-hover:hover {
    /* Generic hover for buttons if needed, specific ones override this */
    transform: translateY(-3px);
}