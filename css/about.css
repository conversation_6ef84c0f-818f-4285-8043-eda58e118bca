    /* Reset and base styles */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Inter', 'Roboto', 'Lato', 'Montserrat', sans-serif;
      }

      body {
        margin: 0;
        padding: 0;
        background: linear-gradient(90deg, #000428 0%, #004E92 100%);
        color: white;
        overflow-x: hidden;
      }

      /* Navigation */
      .nav-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 50px;
        position: relative;
      }

      .logo {
        width: 183px;
        height: 92px;
      }

      .nav-links {
        display: flex;
        gap: 30px;
      }

      .nav-links a {
        color: #C7E5FF;
        text-decoration: none;
        font-size: 16px;
        font-weight: 400;
      }

      .nav-links a:hover {
        color: white;
      }

      .profile-icon {
        width: 25px;
        height: 25px;
        border-radius: 30px;
      }

      /* Hero Section */
      .hero-section {
        display: flex;
        padding: 50px 5%;
        justify-content: space-between;
        align-items: center;
        min-height: 60vh;
      }

      .hero-content {
        max-width: 100%;
        width: 100%;
      }

      .hero-title {
        font-size: 3rem;
        font-weight: 500;
        line-height: 1.2;
        letter-spacing: 1px;
        margin-bottom: 20px;
      }

      .hero-subtitle {
        font-size: 1.8rem;
        font-weight: 500;
        line-height: 1.3;
        margin-bottom: 30px;
      }

      .hero-description {
        font-size: 1.2rem;
        line-height: 1.5;
        margin-bottom: 40px;
      }

      .view-more-btn {
        display: inline-block;
        padding: 10px 20px;
        width: auto;
        min-width: 180px;
        text-align: center;
        border: 2px solid #ffffff;
        border-radius: 4px;
        color: #ffffff;
        font-size: 1rem;
        font-weight: 500;
        text-decoration: none;
        line-height: 1.5;
        transition: all 0.3s ease;
      }

      .view-more-btn:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: translateY(-3px);
      }

      /* Platform Features Section */
      .features-section {
        padding: 50px 5%;
      }

      .features-description {
        width: 100%;
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 30px;
      }

      .features-flex-container {
        display: flex;
        align-items: center;
        gap: 2rem;
        flex-wrap: wrap;
      }

      .features-text {
        flex: 1;
        min-width: 280px;
      }

      .features-image {
        flex: 1;
        min-width: 280px;
        display: flex;
        justify-content: center;
      }

      .features-image img {
        max-width: 100%;
        height: auto;
      }

      /* Steps Section */
      .steps-section {
        padding: 50px 5%;
        text-align: center;
      }

      .steps-title {
        font-size: 2.5rem;
        font-weight: 500;
        margin-bottom: 40px;
      }

      .steps-container {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 20px;
      }

      .step-card {
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.12) 100%);
        border-radius: 16px;
        padding: 30px;
        backdrop-filter: blur(16px);
        flex: 1;
        text-align: left;
        min-width: 280px;
        max-width: 400px;
        min-height: 250px;
      }

      .step-number {
        font-size: 2rem;
        font-weight: 900;
        margin-bottom: 16px;
      }

      .step-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 16px;
      }

      .step-description {
        font-size: 1.1rem;
        font-weight: 300;
        line-height: 1.5;
      }

      /* AWS Innovation Section */
      .aws-innovation {
        margin: 50px 5%;
        padding: 40px 5%;
        background: linear-gradient(136deg, rgba(0, 0, 0, 0.36) 0%, rgba(0, 0, 0, 0.07) 100%);
        border-radius: 24px;
        backdrop-filter: blur(4px);
      }

      .aws-innovation-title {
        font-size: 2.5rem;
        font-weight: 400;
        line-height: 1.3;
        margin-bottom: 30px;
        max-width: 100%;
      }

      .aws-innovation-description {
        font-size: 1.1rem;
        font-weight: 400;
        line-height: 1.6;
      }

      /* AWS Partnership Section */
      .partnership-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 50px 5%;
        flex-wrap: wrap;
        gap: 30px;
      }

      .partnership-content {
        padding: 40px 5%;
        background: linear-gradient(173deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.20) 63%, rgba(0, 0, 0, 0) 100%);
        border-radius: 24px;
        backdrop-filter: blur(8px);
        flex: 1;
        min-width: 280px;
      }

      .partnership-title {
        font-size: 2rem;
        font-weight: 400;
        line-height: 1.3;
        margin-bottom: 24px;
      }

      .partnership-description {
        font-size: 1.1rem;
        font-weight: 400;
        line-height: 1.6;
      }

      .aws-logo {
        max-width: 250px;
        height: auto;
        margin: 0 auto;
      }

      /* Media Queries for Responsive Design */
      @media (max-width: 1200px) {
        .hero-title {
          font-size: 2.5rem;
        }

        .hero-subtitle {
          font-size: 1.5rem;
        }

        .aws-innovation-title,
        .partnership-title {
          font-size: 2rem;
        }
      }

      @media (max-width: 992px) {
        .hero-section {
          padding: 40px 5%;
        }

        .step-card {
          padding: 25px;
        }

        .aws-logo {
          max-width: 200px;
        }
      }

      @media (max-width: 768px) {
        .hero-title {
          font-size: 2rem;
          line-height: 1.3;
        }

        .hero-subtitle {
          font-size: 1.3rem;
        }

        .hero-description {
          font-size: 1rem;
        }

        .steps-title {
          font-size: 2rem;
        }

        .step-card {
          min-width: 100%;
        }

        .aws-innovation-title {
          font-size: 1.8rem;
        }

        .partnership-title {
          font-size: 1.8rem;
        }

        .partnership-section {
          flex-direction: column;
        }

        .aws-logo {
          margin-top: 20px;
        }
      }

      @media (max-width: 576px) {
        .hero-section {
          padding: 30px 5%;
        }

        .hero-title {
          font-size: 1.8rem;
        }

        .hero-subtitle {
          font-size: 1.2rem;
        }

        .view-more-btn {
          width: 100%;
          min-width: unset;
        }

        .features-section,
        .steps-section,
        .aws-innovation,
        .partnership-section {
          padding: 30px 5%;
          margin: 30px 5%;
        }

        .aws-innovation-title,
        .partnership-title,
        .steps-title {
          font-size: 1.5rem;
        }

        .aws-innovation-description,
        .partnership-description,
        .features-description {
          font-size: 1rem;
        }

        .step-number {
          font-size: 1.5rem;
        }

        .step-title {
          font-size: 1.2rem;
        }

        .step-description {
          font-size: 1rem;
        }
      }
