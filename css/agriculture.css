/* Import base styles */
@import 'industry-base.css';

/* Agriculture-specific variables */
:root {
    --earthy-green: #8FBC8F;
    --light-earthy-green: #98FB98;
    --soil-brown: #A0522D;
}

/* Hero Section */
.agriculture-hero {
    background-color: var(--primary-navy);
    color: var(--white);
    padding: 180px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 600px;
}

.agriculture-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(6, 16, 53, 0.9), rgba(10, 29, 69, 0.9));
    z-index: 1;
}

.agriculture-hero-content {
    position: relative;
    z-index: 2;
    max-width: 900px;
    margin: 0 auto;
}

.agriculture-hero h1 {
    font-size: 4rem;
    margin-bottom: 30px;
    color: var(--white);
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.6);
}

.agriculture-hero p {
    color: var(--light-blue);
    font-size: 1.25rem;
    line-height: 1.8;
    margin-bottom: 40px;
}

.hero-illustration {
    height: 380px;
    background: url('../../images/agriculture-hero.png') center/cover no-repeat;
    margin-top: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 176, 255, 0.2);
    transition: transform 0.3s ease-in-out;
}

/* Capabilities Section */
.agriculture-capabilities {
    background-color: var(--white);
    color: var(--text-dark);
}

.capabilities-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(330px, 1fr));
    gap: 45px;
}

.capability-card {
    background: var(--white);
    border: 1px solid rgba(0, 176, 255, 0.3);
    border-radius: 12px;
    padding: 35px;
    text-align: center;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.capability-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 20px 40px rgba(0, 176, 255, 0.2);
}

.capability-icon {
    width: 90px;
    height: 90px;
    margin: 0 auto 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--earthy-green);
    border-radius: 50%;
    box-shadow: 0 5px 15px rgba(78, 248, 10, 0.4);
}

.capability-card h3 {
    color: var(--earthy-green);
    font-size: 1.8rem;
    margin-bottom: 20px;
}

.capability-card ul {
    list-style: none;
    text-align: left;
}

.capability-card li {
    margin-bottom: 12px;
    padding-left: 25px;
    position: relative;
}

.capability-card li::before {
    content: '•';
    color: var(--earthy-green);
    position: absolute;
    left: 0;
}

/* Benefits Section */
.agriculture-benefits {
    background-color: var(--primary-navy);
    color: var(--text-light);
}

.benefits-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 35px;
}

.benefits-list li {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 176, 255, 0.3);
    border-radius: 12px;
    padding: 30px;
    backdrop-filter: blur(12px);
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.benefits-list li:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 176, 255, 0.2);
}

/* Challenges Section */
.agriculture-challenges-addressed {
    background-color: var(--white);
    color: var(--text-dark);
}

.challenges-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 35px;
}

.challenges-list li {
    background: var(--white);
    border: 1px solid rgba(0, 176, 255, 0.3);
    border-radius: 12px;
    padding: 30px;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.challenges-list li:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 176, 255, 0.2);
}

/* Case Studies Section */
.case-studies {
    background-color: var(--primary-navy);
    color: var(--text-light);
}

.case-studies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.case-study-card {
    background: rgb(240, 238, 238);
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease-in-out;
}

.case-study-card:hover {
    transform: translateY(-10px);
}

.case-study-image {
    height: 200px;
    background-size: cover;
    background-position: center;
}

.case-study-content {
    color: #2447a7c0;
    padding: 45px;
}

.case-study-category {
    color: var(--earthy-green);
    font-weight: 500;
    margin-bottom: 10px;
}

.read-more {
    display: inline-block;
    color: var(--accent-blue);
    text-decoration: none;
    margin-top: 15px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: var(--light-blue);
}

/* CTA Section */
.agriculture-cta {
    background: linear-gradient(
        to right,
        rgba(var(--secondary-navy), 0.9),
        rgba(var(--deep-navy), 0.9)
    );
    color: var(--white);
    padding: 100px 0; /* Adjust padding as needed */
    position: relative;
    overflow: hidden;
    display: flex; 
    align-items: center; 
    justify-content: space-between; 
}

.agriculture-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 80% 20%, rgba(0, 176, 255, 0.15), transparent 50%);
    z-index: 1;
    animation: pulse-reverse 7s infinite alternate;
}

@keyframes pulse-reverse {
    0% { opacity: 1; transform: scale(1.03); }
    100% { opacity: 0.8; transform: scale(1); }
}

.cta-content {
    position: relative;
    z-index: 2;
    text-align: left; 
    max-width: 55%; 
    padding-left: 15%; 
}

.agriculture-cta h2 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.agriculture-cta p {
    font-size: 1.25rem;
    color: var(--light-blue);
    margin-bottom: 30px;
}

.cta-button {
    display: inline-block;
    background-color: var(--earthy-green);
    color: var(--deep-navy);
    padding: 16px 35px; /* Adjust button padding if needed */
    border-radius: 10px;
    text-decoration: none;
    font-weight: bold;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
    box-shadow: 0 8px 20px rgba(0, 128, 0, 0.4);
    font-size: 1.1rem;
}

.cta-button:hover {
    transform: scale(1.08);
    box-shadow: 0 12px 25px rgba(0, 128, 0, 0.5);
}

.cta-image-container {
    position: relative;
    z-index: 2;
    width: 35%; 
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
    .agriculture-hero {
        padding: 120px 0;
    }
    
    .agriculture-hero h1 {
        font-size: 3rem;
    }
    
    .hero-illustration {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .agriculture-hero {
        padding: 100px 0;
    }
    
    .agriculture-hero h1 {
        font-size: 2.5rem;
    }
    
    .agriculture-hero p {
        font-size: 1.1rem;
    }
    
    .hero-illustration {
        height: 250px;
    }
    
    .capability-card,
    .benefits-list li,
    .challenges-list li {
        padding: 25px;
    }
}

@media (max-width: 600px) {
    .agriculture-hero {
        padding: 80px 0;
    }
    
    .agriculture-hero h1 {
        font-size: 2rem;
    }
    
    .agriculture-hero p {
        font-size: 1rem;
    }
    
    .hero-illustration {
        height: 200px;
    }
    
    .capability-icon {
        width: 70px;
        height: 70px;
    }
    
    .capability-card h3 {
        font-size: 1.5rem;
    }
    
    .cta-content h2 {
        font-size: 2rem;
    }
}