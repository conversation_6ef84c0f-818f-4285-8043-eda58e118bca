/* Case Studies Page Styles */
:root {
    --primary-color: #061035;
    --secondary-color: #0A1D45;
    --accent-color: #00B0FF;
    --text-color: #E7E7E7;
    --card-bg: rgba(255, 255, 255, 0.05);
    --card-hover-bg: rgba(255, 255, 255, 0.1);
    --transition-speed: 0.3s;
}

.case-studies-container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 120px 40px 80px;
}

.case-studies-header {
    text-align: center;
    margin-bottom: 60px;
}

.case-studies-header h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    background: linear-gradient(90deg, var(--accent-color), #ffffff);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.case-studies-description {
    max-width: 800px;
    margin: 0 auto 40px;
    text-align: center;
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-color);
    opacity: 0.9;
}

.case-studies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.case-study-card {
    background: var(--card-bg);
    border-radius: 12px;
    overflow: hidden;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.case-study-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.case-study-image {
    width: 100%;
    height: 220px;
    object-fit: contain;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.1);
    padding: 10px;
}

.case-study-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.case-study-category {
    display: inline-block;
    background: var(--accent-color);
    color: var(--primary-color);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: uppercase;
}

.case-study-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #ffffff;
    line-height: 1.3;
}

.case-study-card p {
    color: var(--text-color);
    margin-bottom: 20px;
    line-height: 1.6;
    flex-grow: 1;
}

.case-study-link {
    display: inline-flex;
    align-items: center;
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-speed);
    margin-top: auto;
}

.case-study-link:hover {
    color: #ffffff;
}

.case-study-link svg {
    margin-left: 8px;
    transition: transform var(--transition-speed);
}

.case-study-link:hover svg {
    transform: translateX(5px);
}

.case-study-detail-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 120px 40px 80px;
}

.case-study-detail-header {
    margin-bottom: 40px;
}

.case-study-detail-header h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    line-height: 1.3;
}

.case-study-detail-meta {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.case-study-detail-category {
    display: inline-block;
    background: var(--accent-color);
    color: var(--primary-color);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-right: 15px;
    text-transform: uppercase;
}

.case-study-detail-date {
    color: var(--text-color);
    opacity: 0.7;
    font-size: 0.9rem;
}

.case-study-detail-image {
    width: 100%;
    max-height: 500px;
    object-fit: contain;
    border-radius: 12px;
    margin-bottom: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    background-color: rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.case-study-logo-container {
    display: flex;
    justify-content: center;
    margin-bottom: 40px;
}

.case-study-logo-image {
    max-width: 250px;
    max-height: 200px;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    background-color: rgba(255, 255, 255, 0.05);
    padding: 15px;
}

.case-study-detail-content {
    line-height: 1.8;
    color: var(--text-color);
}

.case-study-detail-content h2 {
    font-size: 1.8rem;
    margin: 40px 0 20px;
    color: #ffffff;
}

.case-study-detail-content p {
    margin-bottom: 20px;
}

.case-study-detail-content ul {
    margin-bottom: 20px;
    padding-left: 20px;
}

.case-study-detail-content li {
    margin-bottom: 10px;
}

.case-study-detail-content blockquote {
    border-left: 4px solid var(--accent-color);
    padding-left: 20px;
    margin: 30px 0;
    font-style: italic;
    color: #ffffff;
}

.case-study-detail-results {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 30px;
    margin: 40px 0;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.case-study-detail-results h3 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: #ffffff;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.result-item {
    text-align: center;
}

.result-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 10px;
}

.result-text {
    color: var(--text-color);
    font-size: 0.9rem;
}

.case-study-detail-cta {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    padding: 40px;
    margin: 60px 0;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.case-study-detail-cta h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #ffffff;
}

.case-study-detail-cta p {
    color: var(--text-color);
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    display: inline-block;
    background: var(--accent-color);
    color: var(--primary-color);
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    transition: background-color var(--transition-speed), transform var(--transition-speed);
}

.cta-button:hover {
    background-color: #ffffff;
    transform: translateY(-3px);
}

.pdf-section {
    margin-top: 40px;
    background: var(--card-bg);
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.download-btn {
    display: inline-flex;
    align-items: center;
    background: var(--accent-color);
    color: var(--primary-color);
    padding: 12px 25px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    transition: background-color var(--transition-speed), transform var(--transition-speed);
    margin-top: 20px;
}

.download-btn i {
    margin-right: 10px;
}

.download-btn:hover {
    background-color: #ffffff;
    transform: translateY(-3px);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .case-studies-container,
    .case-study-detail-container {
        padding: 100px 30px 60px;
    }

    .case-studies-header h1,
    .case-study-detail-header h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 992px) {
    .case-studies-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }

    .results-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .case-studies-container,
    .case-study-detail-container {
        padding: 80px 20px 40px;
    }

    .case-studies-header h1,
    .case-study-detail-header h1 {
        font-size: 2rem;
    }

    .case-studies-description {
        font-size: 1rem;
    }

    .case-study-card h3 {
        font-size: 1.3rem;
    }

    .case-study-detail-results {
        padding: 20px;
    }

    .result-number {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .case-studies-grid {
        grid-template-columns: 1fr;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }

    .case-study-detail-cta {
        padding: 30px 20px;
    }
}
