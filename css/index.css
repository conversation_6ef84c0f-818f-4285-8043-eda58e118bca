/* Global Styles */
body {
    background: linear-gradient(135deg, #061035 0%, #0A1D45 50%, #0D2455 100%);
    color: white;
    font-family: 'Inter', sans-serif;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Background Shape */
.background-shape {
    position: absolute;
    top: 0;
    left: 60%;
    right: 0;
    width: 55%;
    height: 100%;
    background: url('../images/background_rotating_logo.png') no-repeat;
    background-size: contain;
    background-position: center right;
    z-index: 0;
    opacity: 0.7;
    animation: rotateImage 20s linear infinite;
    transform-origin: center center;
    pointer-events: none;
}

@keyframes rotateImage {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Main Content */
.main-content {
    position: relative;
    z-index: 1;
}

/* Hero Section */
.hero {
    min-height: 80vh;
    padding: 120px 0 80px;
    display: flex;
    align-items: center;
    position: relative;
}

.hero-content {
    max-width: 600px;
    position: relative;
    z-index: 2;
}

h1 {
    font-size: 4rem;
    font-weight: 500;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero p {
    font-size: 1.25rem;
    line-height: 1.6;
    font-weight: 300;
    margin-bottom: 30px;
}

/* Services Intro Section */
.services-intro {
    padding: 100px 0 60px;
    position: relative;
    z-index: 2;
}

.services-intro-content {
    max-width: 700px;
    margin-left: 70px;
}

h2 {
    font-size: 3rem;
    font-weight: 500;
    margin-bottom: 20px;
}

.services-intro p {
    font-size: 1.125rem;
    line-height: 1.6;
    font-weight: 300;
}

/* Service Cards */
.service-cards {
    padding: 0 0 100px;
    position: relative;
    z-index: 2;
}

.card-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.card {
    background: rgba(9, 17, 46, 0.8);
    border-radius: 12px;
    padding: 30px;
    height: auto;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.card h3 {
    font-size: 1.75rem;
    font-weight: 500;
    margin-bottom: 15px;
    line-height: 1.2;
}

.card p {
    font-size: 1rem;
    font-weight: 300;
    margin-top: auto;
    line-height: 1.5;
}

/* Partnerships Section */
.partnerships {
    padding: 100px 0;
    position: relative;
    z-index: 2;
    overflow: hidden;
}

.partnerships-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.partnerships-title {
    flex: 0 0 30%;
}

.partnerships-title h2 {
    font-size: 2.5rem;
    font-weight: 600;
    margin: 0;
    color: white;
    line-height: 1.2;
}

.partner-logos-container {
    flex: 0 0 70%;
    overflow: hidden;
    position: relative;
    padding: 20px 0;
}

.partner-logos {
    display: flex;
    gap: 50px;
    align-items: center;
    animation: scroll 60s linear infinite;
    width: max-content;
}

.partner-logos img {
    width: auto;
    height: 70px;
    filter: brightness(1.2);
    transition: transform 0.3s ease;
}

.partner-logos img:hover {
    transform: scale(1.1);
}

@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .card-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .partner-logos {
        gap: 50px;
    }

    .background-shape {
        width: 50%;
        opacity: 0.6;
    }

    h1 {
        font-size: 3.5rem;
    }

    h2 {
        font-size: 2.5rem;
    }
}

@media (max-width: 992px) {
    .partnerships-content {
        flex-direction: column;
        text-align: center;
    }

    .partnerships-title {
        flex: 0 0 auto;
        margin-bottom: 40px;
    }

    .partner-logos-container {
        flex: 0 0 auto;
        width: 100%;
    }

    .services-intro-content {
        margin-left: 0;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .hero {
        min-height: 70vh;
        padding: 100px 0 60px;
    }

    h1 {
        font-size: 2.75rem;
    }

    .hero p {
        font-size: 1.125rem;
    }

    .background-shape {
        width: 45%;
        opacity: 0.5;
        left: 55%;
    }

    .card-grid {
        grid-template-columns: 1fr;
    }

    .card {
        padding: 25px;
    }

    .card h3 {
        font-size: 1.5rem;
    }

    .partner-logos {
        gap: 30px;
    }

    .partner-logos img {
        height: 50px;
    }
}

@media (max-width: 576px) {
    .hero {
        min-height: 60vh;
        padding: 80px 0 40px;
    }

    h1 {
        font-size: 2.25rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .services-intro {
        padding: 60px 0 40px;
    }

    h2 {
        font-size: 2rem;
    }

    .services-intro p {
        font-size: 1rem;
    }

    .card {
        min-height: 150px;
    }

    .partner-logos img {
        height: 40px;
    }

    .partner-logos {
        gap: 20px;
    }

    .background-shape {
        width: 40%;
        opacity: 0.4;
        left: 60%;
    }
}