/* Import base styles */
@import url('industry-base.css');

:root {
 
    --energy-primary:  #061035;  
    --energy-secondary:  #061035;   
    --energy-accent: #1A365D;      
    --energy-text: #2D3748;      
    --energy-bg: #EDF2F7;           
    --energy-light: #FFFFFF;        
    --energy-dark: #0A1A2F;       
    --energy-gradient:#09112E;
}

/* Global Typography Improvements */
.energy-section, 
.energy-industry-content,
.industry-section {
    font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
    line-height: 1.7;
}

/*  Hero Section */
.energy-hero, 
.edu-hero {
    background: var(--energy-gradient);
    padding: 100px 0;
    color: var(--energy-light);
    position: relative;
    overflow: hidden;
    background-size: cover;
    background-position: center;
}

.hero-energy {
    background: linear-gradient(rgba(26, 54, 93, 0.85), rgba(26, 54, 93, 0.95)), url('../../images/energy-hero.jpg');
    background-size: cover;
    background-position: center;
    padding: 120px 0 80px;
    color: white;
}

.energy-hero::after, 
.edu-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.energy-hero-content, 
.edu-hero-content,
.edu-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.energy-hero h1, 
.edu-hero-title {
    color: var(--energy-light);
    font-size: 3.5em;
    font-weight: 700;
    margin-bottom: 25px;
    letter-spacing: -0.02em;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.energy-hero p,
.edu-hero-subtitle {
    color: var(--energy-light);
    font-size: 1.25em;
    max-width: 800px;
    margin: 0 auto 40px;
    opacity: 0.9;
}

/*  Capabilities & Solutions Section */
.energy-capabilities,
.industry-section {
    padding: 80px 0;
    background-color: var(--energy-light);
}

.energy-capabilities-header {
    text-align: center;
    margin-bottom: 60px;
}

.energy-capabilities-header h2,
.industry-section h2 {
    color: var(--energy-primary) !important;
    font-size: 2.75em;
    font-weight: 700;
    margin-bottom: 25px;
    text-align: center;
}

.energy-capabilities-grid,
.solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
}

.energy-capability-card,
.energy-solution-card {
    background-color: var(--energy-bg);
    border-radius: 12px;
    padding: 35px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-top: 4px solid var(--energy-accent);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.energy-capability-card:hover,
.energy-solution-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(43, 108, 176, 0.12);
}

.energy-capability-card h3,
.energy-solution-card h3 {
    color: var(--energy-primary);
    font-size: 1.5em;
    margin-bottom: 18px;
    position: relative;
    padding-bottom: 12px;
}

.energy-capability-card h3::after,
.energy-solution-card h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--energy-secondary);
}

.energy-capability-card p,
.energy-solution-card p,
.industry-section p {
    color: var(--energy-text);
    font-size: 1.1em;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* Enhanced Stats Container */
.stats-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 40px 0;
    gap: 20px;
}

.stat-item {
    flex: 1;
    min-width: 200px;
    text-align: center;
    padding: 25px;
    background-color: var(--energy-bg);
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 42px;
    font-weight: 700;
    color: var(--energy-primary);
    margin-bottom: 10px;
}

.stat-label {
    font-size: 16px;
    color: var(--energy-text);
    font-weight: 500;
}

/* Enhanced Case Studies Section */
.energy-case-studies {
    padding: 80px 0;
    background-color: var(--energy-bg);
}

.energy-industry-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
}

.energy-industry-content h2 {
    color: var(--energy-primary) !important;
    font-size: 2.75em !important;
    font-weight: 700;
    text-align: center;
    margin-bottom: 50px;
}

.energy-case-studies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 30px;
}

.energy-case-study {
    background-color: var(--energy-light);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    padding: 0;
    margin-bottom: 30px;
}

.energy-case-study:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(43, 108, 176, 0.15);
}

.energy-case-study-image {
    height: 200px;
    overflow: hidden;
}

.energy-case-study-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.energy-case-study:hover .energy-case-study-image img {
    transform: scale(1.05);
}

.energy-case-study-content,
.energy-case-study {
    padding: 25px;
}

.energy-case-study h3 {
    color: var(--energy-primary) !important;
    font-size: 1.4em;
    margin-bottom: 15px;
    border-left: 4px solid var(--energy-secondary);
    padding-left: 12px;
    background-color: transparent;
}

.energy-case-study p {
    color: var(--energy-text);
    margin-bottom: 20px;
    font-size: 1.05em;
    line-height: 1.6;
}

.energy-case-study-link {
    display: inline-block;
    color: var(--energy-secondary);
    font-weight: 600;
    text-decoration: none;
    padding-bottom: 2px;
    border-bottom: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.energy-case-study-link:hover {
    border-bottom-color: var(--energy-secondary);
}

/* Improved CTA Section */
.energy-cta {
    background: var(--energy-gradient);
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.energy-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle at 50% 50%, rgba(255,255,255,0.1) 0%, transparent 70%);
}

.energy-cta-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 30px;
    position: relative;
    z-index: 2;
}

.energy-cta h2 {
    color: var(--energy-light);
    font-size: 2.75em;
    font-weight: 700;
    margin-bottom: 25px;
}

.energy-cta p {
    color: var(--energy-light);
    font-size: 1.2em;
    margin-bottom: 40px;
    opacity: 0.9;
}

.energy-cta-button {
    display: inline-block;
    padding: 16px 36px;
    background-color: var(--energy-light);
    color: var(--energy-primary);
    font-size: 1.1em;
    font-weight: 600;
    border-radius: 50px;
    text-decoration: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.energy-cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    background-color: #FFFFFF;
}

/* Background shape */
.background-shape {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(43, 108, 176, 0.05) 0%, transparent 50%);
    z-index: -1;
    pointer-events: none;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .energy-hero h1,
    .edu-hero-title {
        font-size: 2.5em;
    }
    
    .energy-capabilities-grid,
    .energy-case-studies-grid,
    .solutions-grid {
        grid-template-columns: 1fr;
    }
    
    .energy-case-study {
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .stats-container {
        flex-direction: column;
    }
    
    .stat-item {
        margin-bottom: 20px;
    }
    
    .energy-industry-content,
    .industry-section {
        padding: 40px 20px;
    }
}