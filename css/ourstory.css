/* Our Story Page Styles */

.story-section {
    padding: 60px 0;
    max-width: 1200px;
    margin: 0 auto;
}

.story-header {
    margin-bottom: 40px;
}

.story-title {
    font-size: 32px;
    font-weight: 500;
    margin-bottom: 10px;
    color: #ffffff;
}

.story-subtitle {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #ffffff;
}

.story-content {
    display: flex;
    margin-bottom: 80px;
    align-items: center;
    gap: 40px;
}

.story-content.reverse {
    flex-direction: row-reverse;
}

.story-text {
    flex: 1;
    padding-right: 20px;
}

.story-image {
    flex: 1;
    max-width: 500px;
    height: auto;
    border-radius: 8px;
    overflow: hidden;
}

.story-image img {
    width: 100%;
    height: auto;
    display: block;
}

.story-paragraph {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #E7E7E7;
}

.story-list {
    list-style-type: disc;
    padding-left: 20px;
    margin-bottom: 15px;
    color: #E7E7E7;
}

.story-list li {
    margin-bottom: 8px;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .story-content {
        flex-direction: column;
        gap: 20px;
    }
    
    .story-content.reverse {
        flex-direction: column;
    }
    
    .story-text, .story-image {
        width: 100%;
        max-width: 100%;
        padding-right: 0;
    }
    
    .story-section {
        padding: 40px 20px;
    }
}
