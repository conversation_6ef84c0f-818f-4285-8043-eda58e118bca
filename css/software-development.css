/* Software Development Services Page Styles */
:root {
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-blue: #00B0FF;
    --accent-purple: #7B68EE;
    --light-blue: #C7E5FF;
    --white: #FFFFFF;
    --off-white: #F4F7FA;
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;
    --shadow-blue: rgba(0, 176, 255, 0.3);
    --shadow-purple: rgba(123, 104, 238, 0.3);
}

/* Global Resets & Base Styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-navy);
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
    overflow-x: hidden;
}

/* Container */
.software-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 2;
}

/* Hero Section */
.software-hero {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
    position: relative;
    overflow: hidden;
}

.software-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(123, 104, 238, 0.1), transparent 40%);
    z-index: 1;
}

.software-hero-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
}

.software-hero-content {
    flex: 1;
    max-width: 600px;
    z-index: 2;
}

.software-hero-image-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

.software-hero-image {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
}

/* Gradient Text */
.gradient-text {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 24px;
    line-height: 1.2;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

/* Button Styles */
.software-btn {
    display: inline-block;
    padding: 12px 28px;
    background-color: var(--accent-purple);
    color: var(--white);
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 12px var(--shadow-purple);
}

.software-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px var(--shadow-purple);
}

.software-hero-btn {
    margin-top: 32px;
}

/* Services Section */
.software-services {
    padding: 100px 0;
    position: relative;
}

.software-section-heading {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    color: var(--white);
}

.software-section-description {
    max-width: 800px;
    margin: 0 auto 60px;
    text-align: center;
    color: var(--text-muted);
    font-size: 1.1rem;
}

.software-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.software-service-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 30px;
    transition: all 0.3s ease;
    border-left: 3px solid var(--accent-purple);
    backdrop-filter: blur(10px);
}

.software-service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.software-service-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--accent-purple);
}

.software-service-description {
    color: var(--text-light);
    font-size: 1rem;
    line-height: 1.6;
}

/* Stats Section */
.software-stats {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(6, 16, 53, 0.8), rgba(10, 29, 69, 0.8));
    position: relative;
}

.software-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.software-stat-item {
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
    border-bottom: 3px solid var(--accent-purple);
}

.software-stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.software-stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-purple);
    margin-bottom: 10px;
}

.software-stat-label {
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Process Section */
.software-process {
    padding: 100px 0;
    position: relative;
}

.process-steps {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    margin-top: 50px;
}

.process-step {
    flex: 1;
    min-width: 200px;
    max-width: 300px;
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
}

.process-step:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.process-step-number {
    width: 50px;
    height: 50px;
    background: var(--accent-purple);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
    margin: 0 auto 20px;
}

.process-step-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 15px;
}

.process-step-description {
    color: var(--text-muted);
    font-size: 0.95rem;
}

/* Technologies Section */
.software-technologies {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(6, 16, 53, 0.8), rgba(10, 29, 69, 0.8));
    position: relative;
}

.software-tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.software-tech-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.software-tech-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
}

.software-tech-icon {
    fill: var(--accent-purple);
    transition: fill 0.3s ease;
    width: 60px;
    height: 60px;
    margin-bottom: 15px;
}

.software-tech-item:hover .software-tech-icon {
    fill: var(--accent-blue);
}

.software-tech-item p {
    color: var(--text-light);
    font-weight: 500;
}

/* Projects Section */
.software-projects {
    padding: 100px 0;
    position: relative;
}

.software-projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.software-project-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.software-project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.project-content {
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.project-text {
    flex: 1;
    padding: 30px;
    position: relative;
    color: var(--text-light);
}

.project-tag {
    display: inline-block;
    padding: 5px 10px;
    background-color: var(--accent-purple);
    color: var(--white);
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 15px;
}

.project-text h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--white);
}

.project-text p {
    color: var(--text-muted);
    margin-bottom: 20px;
}

.project-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: transparent;
    color: var(--accent-purple);
    border: 1px solid var(--accent-purple);
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
}

.project-button:hover {
    background-color: var(--accent-purple);
    color: var(--white);
}

/* Floating Animation */
.floating-element {
    animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
}

/* Dynamic Shadow */
.dynamic-shadow {
    filter: drop-shadow(0 10px 20px rgba(123, 104, 238, 0.3));
}

/* Button Hover Effect */
.button-hover {
    position: relative;
    overflow: hidden;
}

.button-hover::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.button-hover:hover::after {
    transform: translateX(0);
}

/* Scroll Reveal Animation */
.scroll-reveal {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Code Block Styling */
.code-block {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: var(--accent-purple);
    overflow-x: auto;
    border-left: 3px solid var(--accent-purple);
}

.code-block pre {
    margin: 0;
}

.code-comment {
    color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .gradient-text {
        font-size: 3rem;
    }

    .software-section-heading {
        font-size: 2.2rem;
    }
}

@media (max-width: 992px) {
    .software-container {
        padding: 0 30px;
    }

    .software-hero-wrapper {
        flex-direction: column;
    }

    .software-hero-content {
        max-width: 100%;
        text-align: center;
    }

    .software-hero-image-container {
        margin-top: 40px;
    }

    .software-services-grid,
    .software-projects-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .software-container {
        padding: 0 20px;
    }

    .gradient-text {
        font-size: 2.5rem;
    }

    .software-section-heading {
        font-size: 2rem;
    }

    .software-section-description {
        font-size: 1rem;
    }

    .software-hero,
    .software-services,
    .software-stats,
    .software-process,
    .software-technologies,
    .software-projects {
        padding: 60px 0;
    }

    .software-stats-grid {
        grid-template-columns: 1fr;
    }

    .process-steps {
        flex-direction: column;
        align-items: center;
    }

    .process-step {
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    .gradient-text {
        font-size: 2rem;
    }

    .software-section-heading {
        font-size: 1.8rem;
    }

    .software-hero,
    .software-services,
    .software-stats,
    .software-process,
    .software-technologies,
    .software-projects {
        padding: 40px 0;
    }

    .software-service-card,
    .software-stat-item,
    .process-step,
    .project-content {
        padding: 20px;
    }

    .software-stat-number {
        font-size: 2.5rem;
    }

    .software-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}