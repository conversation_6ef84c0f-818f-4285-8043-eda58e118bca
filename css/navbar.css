/* NAVIGATION */
:root {
    --primary-color: #061035;
    --secondary-color: #0A1D45;
    --text-color: #C7E5FF;
    --hover-color: #ffffff;
    --border-radius: 8px;
    --transition-speed: 0.3s;
}

.navbar-header {
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: 0;
    position: relative;
    z-index: 100;
    width: 100%;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 40px;
    max-width: 1400px;
    margin: 0 auto;
    background: transparent;
}

.navbar-logo-container {
    flex: 0 0 auto;
}

.navbar-nav {
    display: flex;
    justify-content: center;
    flex: 1;
}

.navbar-right {
    display: flex;
    align-items: center;
}

.navbar-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
    align-items: center;
}

.navbar-center {
    justify-content: center;
}

.navbar-links li {
    position: relative;
}

.navbar-links a {
    text-decoration: none;
    color: var(--text-color);
    padding: 10px 0;
    display: block;
    font-weight: 500;
    transition: all 0.2s ease;
    font-size: 15px;
    position: relative;
}

.navbar-links a:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: white;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.navbar-links a:hover:before {
    transform: scaleX(1);
}

.navbar-links a:hover {
    color: var(--hover-color);
}

/* Active dropdown styling */
.navbar-dropdown.active > a {
    color: var(--hover-color);
}

.navbar-dropdown.active > a:before {
    transform: scaleX(1);
}

/* Logo */
.navbar-logo {
    height: 40px;
    display: block;
}

/* Profile Icon */
.navbar-profile-icon {
    height: 24px;
    border-radius: 50%;
    display: block;
}

/* DROPDOWN MENU - Full Width Style */
.navbar-dropdown-container {
    display: none;
    position: absolute;
    left: 0;
    width: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
    z-index: 999;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 40px 0;
}

.navbar-dropdown-container.active {
    display: block;
}

.dropdown-content-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    display: flex;
}

.dropdown-columns {
    display: flex;
    gap: 0;
    width: 100%;
}

.dropdown-column {
    flex: 1;
    padding: 0 40px 0 0;
}

.dropdown-column h3 {
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 20px;
}

.dropdown-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dropdown-column li {
    margin: 15px 0;
}

.dropdown-column a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 14px;
    display: block;
    padding: 0;
    transition: color 0.2s;
}

.dropdown-column a:hover {
    color: var(--hover-color);
}

/* Vertical line between sections */
.dropdown-divider {
    width: 1px;
    background-color: rgba(255, 255, 255, 0.2);
    margin: 0 40px;
}

/* Hamburger Menu */
.hamburger-menu {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
    z-index: 1000;
    margin-left: auto;
    margin-right: 20px;
}

.hamburger-menu span {
    display: block;
    height: 3px;
    width: 100%;
    background-color: var(--text-color);
    border-radius: 3px;
    transition: all 0.3s ease;
}

.hamburger-menu.active span:nth-child(1) {
    transform: translateY(9px) rotate(45deg);
}

.hamburger-menu.active span:nth-child(2) {
    opacity: 0;
}

.hamburger-menu.active span:nth-child(3) {
    transform: translateY(-9px) rotate(-45deg);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .navbar-container {
        padding: 15px 20px;
    }

    .dropdown-content-wrapper {
        padding: 0 20px;
    }

    .dropdown-column {
        padding: 0 20px 0 0;
    }

    .dropdown-divider {
        margin: 0 20px;
    }
}

@media (max-width: 768px) {
    /* Show hamburger menu */
    .hamburger-menu {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 30px;
        height: 20px;
        cursor: pointer;
        z-index: 1000;
        position: relative;
    }

    .hamburger-menu span {
        display: block;
        width: 100%;
        height: 2px;
        background-color: var(--text-color);
        transition: var(--transition-speed) ease;
    }

    .hamburger-menu.active span:nth-child(1) {
        transform: translateY(9px) rotate(45deg);
    }

    .hamburger-menu.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger-menu.active span:nth-child(3) {
        transform: translateY(-9px) rotate(-45deg);
    }

    .navbar-container {
        flex-wrap: wrap;
        padding: 15px 20px;
    }

    /* Mobile Navigation */
    .navbar-nav {
        display: none;
        width: 100%;
        order: 3;
        margin-top: 15px;
        background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border-radius: var(--border-radius);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        padding: 10px;
        max-height: 80vh;
        overflow-y: auto;
    }

    .navbar-nav.active {
        display: block;
        animation: slideDown 0.3s ease forwards;
    }

    .navbar-links {
        flex-direction: column;
        gap: 0;
    }

    .navbar-links li {
        width: 100%;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .navbar-links li:last-child {
        border-bottom: none;
    }

    .navbar-links a {
        padding: 15px;
        font-size: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* Dropdown arrow indicator */
    .navbar-dropdown > a::after {
        content: '';
        width: 8px;
        height: 8px;
        border-right: 2px solid var(--text-color);
        border-bottom: 2px solid var(--text-color);
        transform: rotate(45deg);
        transition: transform 0.3s ease;
        margin-left: 10px;
    }

    .navbar-dropdown.active > a::after {
        transform: rotate(-135deg);
    }

    .navbar-right {
        display: none;
        width: 100%;
        order: 4;
        justify-content: center;
        padding: 15px 0;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .navbar-right.active {
        display: flex;
    }

    .navbar-profile-icon {
        margin: 0;
    }

    /* Mobile Dropdown Styling */
    .navbar-dropdown-container {
        display: none; /* Hide the original dropdown containers on mobile */
    }

    /* New mobile dropdown styling */
    .mobile-dropdown-content {
        display: none;
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        padding: 0;
        margin: 0;
        overflow: hidden;
    }

    .navbar-dropdown.active .mobile-dropdown-content {
        display: block;
        animation: slideDown 0.3s ease forwards;
    }

    .mobile-dropdown-item {
        list-style: none;
        margin: 0;
        padding: 0;
        border-top: 1px solid rgba(255, 255, 255, 0.05);
    }

    .mobile-dropdown-item a {
        padding: 12px 15px 12px 30px;
        font-size: 14px;
        opacity: 0.9;
        display: block;
        color: var(--text-color);
        text-decoration: none;
        transition: var(--transition-speed);
    }

    .mobile-dropdown-item a:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.1);
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

@media (max-width: 480px) {
    .navbar-container {
        padding: 10px 15px;
    }

    .navbar-logo {
        height: 32px;
    }

    .hamburger-menu {
        width: 25px;
        height: 18px;
    }
}