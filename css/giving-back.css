/* Giving Back Page Styles */
:root {
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-blue: #00B0FF;
    --light-blue: #4DC3FF;
    --white: #FFFFFF;
    --off-white: #F4F7FA;
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;
    --shadow-blue: rgba(0, 176, 255, 0.3);

    /* Shadows */
    --shadow-sm: 0 4px 6px rgba(0, 176, 255, 0.1);
    --shadow-md: 0 8px 15px rgba(0, 176, 255, 0.15);
    --shadow-lg: 0 15px 30px rgba(0, 176, 255, 0.2);

    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 2rem;
    --space-lg: 4rem;
    --space-xl: 6rem;

    /* Typography */
    --font-size-xs: 0.875rem;
    --font-size-sm: 1rem;
    --font-size-md: 1.25rem;
    --font-size-lg: 1.5rem;
    --font-size-xl: 2rem;
    --font-size-2xl: 2.5rem;
    --font-size-3xl: 3.5rem;
}

/* Base Styles */
.giving-content {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-navy);
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
}

.giving-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 2;
}

/* Hero Section */
.giving-hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
    position: relative;
    overflow: hidden;
}

.giving-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(0, 176, 255, 0.1), transparent 40%);
    z-index: 1;
}

.giving-hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.giving-hero-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--space-md);
    line-height: 1.2;
    color: var(--white);
}

.giving-hero-subtitle {
    font-size: var(--font-size-md);
    margin-bottom: var(--space-lg);
    color: var(--text-light);
}

/* Content Section */
.giving-section {
    padding: var(--space-xl) 0;
    position: relative;
}

.giving-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 80% 20%, rgba(0, 176, 255, 0.05), transparent 40%);
    z-index: 1;
}

.giving-section-heading {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--space-sm);
    color: var(--white);
    position: relative;
    padding-bottom: var(--space-sm);
}

.giving-section-heading::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-blue), var(--light-blue));
    border-radius: 3px;
}

.giving-section-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto var(--space-lg);
    color: var(--text-muted);
    font-size: var(--font-size-md);
}

/* Initiatives Grid */
.initiatives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-md);
    position: relative;
    z-index: 2;
}

.initiative-card {
    background: var(--secondary-navy);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
    border-top: 4px solid var(--accent-blue);
    padding-top: 20px;
}

.initiative-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.initiative-content {
    padding: var(--space-md);
    position: relative;
    z-index: 1;
}

.initiative-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    color: var(--white);
}

.initiative-description {
    color: var(--text-muted);
    margin-bottom: var(--space-sm);
}

/* Impact Section */
.impact-section {
    background: var(--secondary-navy);
    padding: var(--space-xl) 0;
    position: relative;
}

.impact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 40%, rgba(0, 176, 255, 0.05), transparent 40%);
    z-index: 1;
}

.impact-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    margin: var(--space-lg) 0;
    position: relative;
    z-index: 2;
}

.impact-stat {
    background: var(--deep-navy);
    padding: var(--space-md);
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border-bottom: 3px solid var(--accent-blue);
}

.impact-stat:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.impact-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--accent-blue);
    margin-bottom: var(--space-xs);
}

.impact-label {
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

/* Partners Section */
.partners-section {
    text-align: center;
    margin-top: var(--space-xl);
    position: relative;
    z-index: 2;
}

.partners-logos {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--space-lg);
    margin-top: var(--space-md);
}

.partner-logo {
    max-width: 150px;
    height: auto;
    opacity: 0.7;
    transition: all 0.3s ease;
    filter: brightness(0) invert(1);
}

.partner-logo:hover {
    opacity: 1;
    transform: scale(1.05);
}

/* Get Involved Section */
.get-involved {
    background: var(--primary-navy);
    padding: var(--space-xl) 0;
    text-align: center;
    position: relative;
}

.get-involved::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(0, 176, 255, 0.1), transparent 60%);
    z-index: 1;
}

.get-involved-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.contact-button {
    display: inline-block;
    background: var(--accent-blue);
    color: var(--white);
    padding: 12px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.contact-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent-blue), var(--light-blue));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.contact-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.contact-button:hover::before {
    opacity: 1;
}

/* Animation Classes */
.scroll-reveal {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

.floating-element {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .giving-hero-title {
        font-size: var(--font-size-2xl);
    }
}

@media (max-width: 992px) {
    .giving-container {
        padding: 0 30px;
    }

    .initiatives-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .giving-hero {
        padding: 80px 0 60px;
    }

    .giving-hero-title {
        font-size: var(--font-size-xl);
    }

    .initiatives-grid,
    .impact-stats {
        grid-template-columns: 1fr;
    }

    .partners-logos {
        gap: var(--space-md);
    }
}

@media (max-width: 576px) {
    .giving-container {
        padding: 0 20px;
    }

    .giving-hero-title {
        font-size: var(--font-size-lg);
    }

    .giving-section-heading {
        font-size: var(--font-size-xl);
    }

    .contact-button {
        padding: 10px 20px;
    }
}