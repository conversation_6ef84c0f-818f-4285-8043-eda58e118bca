/* Data Analytics Services Page Styles */
:root {
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-green: #00C853;
    --accent-teal: #00BFA5;
    --light-green: #B9F6CA;
    --white: #FFFFFF;
    --off-white: #F4F7FA;
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;
    --shadow-green: rgba(0, 200, 83, 0.3);
    --shadow-teal: rgba(0, 191, 165, 0.3);
}

/* Global Resets & Base Styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-navy);
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
    overflow-x: hidden;
}

/* Container */
.analytics-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 2;
}

/* Hero Section */
.analytics-hero {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
    position: relative;
    overflow: hidden;
}

.analytics-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(0, 200, 83, 0.1), transparent 40%);
    z-index: 1;
}

.analytics-hero-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
}

.analytics-hero-content {
    flex: 1;
    max-width: 600px;
    z-index: 2;
}

.analytics-hero-image-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

.analytics-hero-image {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
}

/* Gradient Text */
.gradient-text {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 24px;
    line-height: 1.2;
    background: linear-gradient(90deg, var(--accent-green), var(--accent-teal));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

/* Button Styles */
.analytics-btn {
    display: inline-block;
    padding: 12px 28px;
    background-color: var(--accent-green);
    color: var(--white);
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 12px var(--shadow-green);
}

.analytics-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px var(--shadow-green);
}

.analytics-hero-btn {
    margin-top: 32px;
}

/* Services Section */
.analytics-services {
    padding: 100px 0;
    position: relative;
}

.analytics-section-heading {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    color: var(--white);
}

.analytics-section-description {
    max-width: 800px;
    margin: 0 auto 60px;
    text-align: center;
    color: var(--text-muted);
    font-size: 1.1rem;
}

.analytics-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.analytics-service-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 30px;
    transition: all 0.3s ease;
    border-left: 3px solid var(--accent-green);
    backdrop-filter: blur(10px);
}

.analytics-service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.analytics-service-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--accent-green);
}

.analytics-service-description {
    color: var(--text-light);
    font-size: 1rem;
    line-height: 1.6;
}

/* Stats Section */
.analytics-stats {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(6, 16, 53, 0.8), rgba(10, 29, 69, 0.8));
    position: relative;
}

.analytics-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.analytics-stat-item {
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
    border-bottom: 3px solid var(--accent-green);
}

.analytics-stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.analytics-stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-green);
    margin-bottom: 10px;
}

.analytics-stat-label {
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Process Section */
.analytics-process {
    padding: 100px 0;
    position: relative;
}

.process-steps {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    margin-top: 50px;
}

.process-step {
    flex: 1;
    min-width: 200px;
    max-width: 300px;
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
}

.process-step:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.process-step-number {
    width: 50px;
    height: 50px;
    background: var(--accent-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
    margin: 0 auto 20px;
}

.process-step-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 15px;
}

.process-step-description {
    color: var(--text-muted);
    font-size: 0.95rem;
}

/* Case Studies Section */
.analytics-case-studies {
    padding: 100px 0;
    position: relative;
}

.analytics-case-studies-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.analytics-case-study-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.analytics-case-study-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.case-study-content-wrapper {
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.case-study-text {
    flex: 1;
    padding: 30px;
    position: relative;
    color: var(--text-light);
}

.case-study-step-number {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-green);
    opacity: 0.7;
}

.case-study-text h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--white);
}

.case-study-text p {
    color: var(--text-muted);
    margin-bottom: 20px;
}

.case-study-hidden-content {
    display: none;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.case-study-hidden-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--accent-green);
}

.case-study-hidden-content p {
    margin-bottom: 20px;
}

.case-study-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: transparent;
    color: var(--accent-green);
    border: 1px solid var(--accent-green);
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
}

.case-study-button:hover {
    background-color: var(--accent-green);
    color: var(--white);
}

/* Technologies Section */
.analytics-technologies {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(6, 16, 53, 0.8), rgba(10, 29, 69, 0.8));
    position: relative;
}

.analytics-tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.analytics-tech-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.analytics-tech-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
}

.analytics-tech-icon {
    fill: var(--accent-green);
    transition: fill 0.3s ease;
    width: 60px;
    height: 60px;
    margin-bottom: 15px;
}

.analytics-tech-item:hover .analytics-tech-icon {
    fill: var(--accent-teal);
}

.analytics-tech-item p {
    color: var(--text-light);
    font-weight: 500;
}

/* Data Visualization Section */
.analytics-visualization {
    padding: 100px 0;
    position: relative;
}

.visualization-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 30px;
    margin-top: 50px;
    overflow: hidden;
    position: relative;
}

.visualization-chart {
    width: 100%;
    height: 400px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.chart-bar {
    position: absolute;
    bottom: 0;
    width: 40px;
    background: linear-gradient(to top, var(--accent-green), var(--accent-teal));
    border-radius: 4px 4px 0 0;
    transition: height 1s ease;
}

.chart-bar:nth-child(1) {
    left: 10%;
    height: 60%;
}

.chart-bar:nth-child(2) {
    left: 25%;
    height: 80%;
}

.chart-bar:nth-child(3) {
    left: 40%;
    height: 40%;
}

.chart-bar:nth-child(4) {
    left: 55%;
    height: 70%;
}

.chart-bar:nth-child(5) {
    left: 70%;
    height: 90%;
}

.chart-bar:nth-child(6) {
    left: 85%;
    height: 50%;
}

.chart-label {
    position: absolute;
    bottom: -30px;
    transform: translateX(-50%);
    color: var(--text-muted);
    font-size: 0.8rem;
}

.chart-value {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--accent-green);
    color: var(--white);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.chart-bar:hover .chart-value {
    opacity: 1;
}

/* Floating Animation */
.floating-element {
    animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
}

/* Dynamic Shadow */
.dynamic-shadow {
    filter: drop-shadow(0 10px 20px rgba(0, 200, 83, 0.3));
}

/* Button Hover Effect */
.button-hover {
    position: relative;
    overflow: hidden;
}

.button-hover::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.button-hover:hover::after {
    transform: translateX(0);
}

/* Scroll Reveal Animation */
.scroll-reveal {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(6, 16, 53, 0.9);
    z-index: 1000;
    overflow-y: auto;
    padding: 50px 0;
}

.modal-content {
    max-width: 800px;
    margin: 0 auto;
    background: var(--primary-navy);
    border-radius: 12px;
    padding: 40px;
    position: relative;
    border: 1px solid rgba(0, 200, 83, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.close-modal {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 1.5rem;
    color: var(--text-light);
    background: none;
    border: none;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--accent-green);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .gradient-text {
        font-size: 3rem;
    }

    .analytics-section-heading {
        font-size: 2.2rem;
    }
}

@media (max-width: 992px) {
    .analytics-container {
        padding: 0 30px;
    }

    .analytics-hero-wrapper {
        flex-direction: column;
    }

    .analytics-hero-content {
        max-width: 100%;
        text-align: center;
    }

    .analytics-hero-image-container {
        margin-top: 40px;
    }

    .analytics-services-grid,
    .analytics-case-studies-list {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .visualization-chart {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .analytics-container {
        padding: 0 20px;
    }

    .gradient-text {
        font-size: 2.5rem;
    }

    .analytics-section-heading {
        font-size: 2rem;
    }

    .analytics-section-description {
        font-size: 1rem;
    }

    .analytics-hero,
    .analytics-services,
    .analytics-stats,
    .analytics-process,
    .analytics-case-studies,
    .analytics-technologies,
    .analytics-visualization {
        padding: 60px 0;
    }

    .analytics-stats-grid {
        grid-template-columns: 1fr;
    }

    .process-steps {
        flex-direction: column;
        align-items: center;
    }

    .process-step {
        max-width: 100%;
    }

    .modal-content {
        padding: 30px 20px;
    }

    .visualization-chart {
        height: 250px;
    }
}

@media (max-width: 576px) {
    .gradient-text {
        font-size: 2rem;
    }

    .analytics-section-heading {
        font-size: 1.8rem;
    }

    .analytics-hero,
    .analytics-services,
    .analytics-stats,
    .analytics-process,
    .analytics-case-studies,
    .analytics-technologies,
    .analytics-visualization {
        padding: 40px 0;
    }

    .analytics-service-card,
    .analytics-stat-item,
    .process-step,
    .case-study-content-wrapper {
        padding: 20px;
    }

    .analytics-stat-number {
        font-size: 2.5rem;
    }

    .analytics-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .visualization-chart {
        height: 200px;
    }
}