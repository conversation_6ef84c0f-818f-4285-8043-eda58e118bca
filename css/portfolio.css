* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #f4f4f4 0%, #e9e9e9 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.portfolio-container {
    width: 100%;
    max-width: 1200px;
    margin: 40px auto;
    padding: 40px;
    background: white;
    border-radius: 16px;
    box-shadow: 
        0 10px 25px rgba(0, 0, 0, 0.1),
        0 20px 50px rgba(0, 123, 255, 0.08);
    position: relative;
    overflow: hidden;
}

.portfolio-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.portfolio-header h1 {
    color: #007bff;
    font-size: 2.5rem;
    font-weight: 700;
    position: relative;
    display: inline-block;
}

.portfolio-header h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #007bff, #0056b3);
    border-radius: 2px;
}

.portfolio-description {
    max-width: 800px;
    margin: 0 auto 30px;
    text-align: center;
    color: #555;
    font-size: 1.1rem;
}

.pdf-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.pdf-viewer {
    width: 100%;
    min-height: 900px;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(0, 123, 255, 0.1);
}

.download-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px 30px;
    background: linear-gradient(to right, #007bff, #0056b3);
    color: white;
    text-decoration: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 6px 15px rgba(0, 123, 255, 0.3);
}

.download-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.4);
    background: linear-gradient(to right, #0056b3, #004085);
}

.download-btn i {
    font-size: 1.2rem;
}

@media screen and (max-width: 768px) {
    .portfolio-container {
        margin: 20px;
        padding: 20px;
    }

    .portfolio-header h1 {
        font-size: 2rem;
    }

    .pdf-viewer {
        min-height: 400px;
    }
}