/* Import base styles */
@import url('industry-base.css');

:root {
    --mining-primary: #061035;  
    --mining-secondary: #061035;   
    --mining-accent: #1A365D;      
    --mining-text: #2D3748;      
    --mining-bg: #EDF2F7;           
    --mining-light: #FFFFFF;        
    --mining-dark: #0A1A2F;       
    --mining-gradient: #09112E;
}

/* Global Typography Improvements */
.mining-section, 
.mining-industry-content,
.industry-section {
    font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
    line-height: 1.7;
}

/* Hero Section */
.mining-hero {
    background: var(--mining-gradient);
    padding: 100px 0;
    color: var(--mining-light);
    position: relative;
    overflow: hidden;
}

.mining-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/mining-hero-bg.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.2;
    z-index: 0;
}

.mining-hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

.mining-hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(90deg, #FFFFFF, #A0B4D4);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.mining-hero p {
    font-size: 1.2rem;
    max-width: 600px;
    margin-bottom: 30px;
}

/* Overview Section */
.mining-overview {
    padding: 80px 0;
    background-color: var(--mining-light);
}

.mining-overview-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 40px;
}

.mining-overview-content {
    flex: 1;
    min-width: 300px;
}

.mining-overview h2 {
    font-size: 2.5rem;
    color: var(--mining-primary);
    margin-bottom: 20px;
    position: relative;
}

.mining-overview h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: var(--accent-blue);
}

.mining-overview p {
    margin-bottom: 20px;
    color: var(--mining-text);
}

.mining-overview-image {
    flex: 1;
    min-width: 300px;
    position: relative;
}

.mining-overview-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Solutions Section */
.mining-solutions {
    padding: 80px 0;
    background-color: var(--mining-bg);
}

.mining-solutions-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.mining-solutions h2 {
    font-size: 2.5rem;
    color: var(--mining-primary);
    text-align: center;
    margin-bottom: 50px;
    position: relative;
}

.mining-solutions h2::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--accent-blue);
}

.mining-solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.mining-solution-card {
    background: var(--mining-light);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s, box-shadow 0.3s;
}

.mining-solution-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.mining-solution-card h3 {
    font-size: 1.5rem;
    color: var(--mining-primary);
    margin-bottom: 15px;
}

.mining-solution-card p {
    color: var(--mining-text);
}

/* Benefits Section */
.mining-benefits {
    padding: 80px 0;
    background-color: var(--mining-light);
}

.mining-benefits-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.mining-benefits h2 {
    font-size: 2.5rem;
    color: var(--mining-primary);
    text-align: center;
    margin-bottom: 50px;
    position: relative;
}

.mining-benefits h2::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--accent-blue);
}

.mining-benefits-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.mining-benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.mining-benefit-icon {
    width: 50px;
    height: 50px;
    background: var(--mining-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--mining-light);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.mining-benefit-content h3 {
    font-size: 1.3rem;
    color: var(--mining-primary);
    margin-bottom: 10px;
}

.mining-benefit-content p {
    color: var(--mining-text);
}

/* CTA Section */
.mining-cta {
    padding: 80px 0;
    background: var(--mining-gradient);
    color: var(--mining-light);
    position: relative;
    overflow: hidden;
}

.mining-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/mining-cta-bg.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    z-index: 0;
}

.mining-cta-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.mining-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.mining-cta p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.mining-cta-button {
    display: inline-block;
    background: var(--accent-blue);
    color: var(--mining-light);
    padding: 15px 30px;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    transition: background 0.3s, transform 0.3s;
}

.mining-cta-button:hover {
    background: #0090d0;
    transform: translateY(-3px);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .mining-hero h1 {
        font-size: 2.5rem;
    }
    
    .mining-overview-container,
    .mining-solutions-grid,
    .mining-benefits-list {
        flex-direction: column;
    }
    
    .mining-overview h2,
    .mining-solutions h2,
    .mining-benefits h2,
    .mining-cta h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .mining-hero h1 {
        font-size: 2rem;
    }
    
    .mining-hero p,
    .mining-cta p {
        font-size: 1rem;
    }
    
    .mining-overview h2,
    .mining-solutions h2,
    .mining-benefits h2,
    .mining-cta h2 {
        font-size: 1.8rem;
    }
}
