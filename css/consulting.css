/* Consulting Services Page Styles */
:root {
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-blue: #00B0FF;
    --light-blue: #C7E5FF;
    --white: #FFFFFF;
    --off-white: #F4F7FA;
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;
    --shadow-blue: rgba(0, 176, 255, 0.3);
}

/* Global Resets & Base Styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-navy);
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
    overflow-x: hidden;
}

/* Container */
.consulting-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 2;
}

/* Hero Section */
.consulting-hero {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
    position: relative;
    overflow: hidden;
}

.consulting-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(0, 176, 255, 0.1), transparent 40%);
    z-index: 1;
}

.consulting-hero-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
}

.consulting-hero-content {
    flex: 1;
    max-width: 600px;
    z-index: 2;
}

.consulting-hero-image-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

.consulting-hero-image {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
}

/* Gradient Text */
.gradient-text {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 24px;
    line-height: 1.2;
    background: linear-gradient(90deg, var(--accent-blue), var(--light-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

/* Button Styles */
.consulting-btn {
    display: inline-block;
    padding: 12px 28px;
    background-color: var(--accent-blue);
    color: var(--white);
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 12px var(--shadow-blue);
}

.consulting-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px var(--shadow-blue);
}

.consulting-hero-btn {
    margin-top: 32px;
}

/* Capabilities Section */
.consulting-capabilities {
    padding: 100px 0;
    position: relative;
}

.consulting-section-heading {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    color: var(--white);
}

.consulting-section-description {
    max-width: 800px;
    margin: 0 auto 60px;
    text-align: center;
    color: var(--text-muted);
    font-size: 1.1rem;
}

.consulting-capabilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.consulting-capability-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 30px;
    transition: all 0.3s ease;
    border-left: 3px solid var(--accent-blue);
    backdrop-filter: blur(10px);
}

.consulting-capability-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.consulting-capability-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--accent-blue);
}

.consulting-capability-description {
    color: var(--text-light);
    font-size: 1rem;
    line-height: 1.6;
}

/* Stats Section */
.consulting-stats {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(6, 16, 53, 0.8), rgba(10, 29, 69, 0.8));
    position: relative;
}

.consulting-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.consulting-stat-item {
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
    border-bottom: 3px solid var(--accent-blue);
}

.consulting-stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.consulting-stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-blue);
    margin-bottom: 10px;
}

.consulting-stat-label {
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Case Studies Section */
.consulting-case-studies {
    padding: 100px 0;
    position: relative;
}

.consulting-case-studies-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.consulting-case-study-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.consulting-case-study-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.case-study-content-wrapper {
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.case-study-text {
    flex: 1;
    padding: 30px;
    position: relative;
    color: var(--text-light);
}

.case-study-step-number {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-blue);
    opacity: 0.7;
}

.case-study-text h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--white);
}

.case-study-text p {
    color: var(--text-muted);
    margin-bottom: 20px;
}

.case-study-hidden-content {
    display: none;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.case-study-hidden-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--accent-blue);
}

.case-study-hidden-content p {
    margin-bottom: 20px;
}

.case-study-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: transparent;
    color: var(--accent-blue);
    border: 1px solid var(--accent-blue);
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
}

.case-study-button:hover {
    background-color: var(--accent-blue);
    color: var(--white);
}

/* Methodology Section */
.consulting-methodology {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(6, 16, 53, 0.8), rgba(10, 29, 69, 0.8));
    position: relative;
}

.methodology-steps {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    margin-top: 50px;
}

.methodology-step {
    flex: 1;
    min-width: 200px;
    max-width: 300px;
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
}

.methodology-step:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.methodology-step-number {
    width: 50px;
    height: 50px;
    background: var(--accent-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
    margin: 0 auto 20px;
}

.methodology-step-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 15px;
}

.methodology-step-description {
    color: var(--text-muted);
    font-size: 0.95rem;
}

/* Floating Animation */
.floating-element {
    animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
}

/* Dynamic Shadow */
.dynamic-shadow {
    filter: drop-shadow(0 10px 20px rgba(0, 176, 255, 0.3));
}

/* Button Hover Effect */
.button-hover {
    position: relative;
    overflow: hidden;
}

.button-hover::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.button-hover:hover::after {
    transform: translateX(0);
}

/* Scroll Reveal Animation */
.scroll-reveal {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(6, 16, 53, 0.9);
    z-index: 1000;
    overflow-y: auto;
    padding: 50px 0;
}

.modal-content {
    max-width: 800px;
    margin: 0 auto;
    background: var(--primary-navy);
    border-radius: 12px;
    padding: 40px;
    position: relative;
    border: 1px solid rgba(0, 176, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.close-modal {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 1.5rem;
    color: var(--text-light);
    background: none;
    border: none;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--accent-blue);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .gradient-text {
        font-size: 3rem;
    }

    .consulting-section-heading {
        font-size: 2.2rem;
    }
}

@media (max-width: 992px) {
    .consulting-container {
        padding: 0 30px;
    }

    .consulting-hero-wrapper {
        flex-direction: column;
    }

    .consulting-hero-content {
        max-width: 100%;
        text-align: center;
    }

    .consulting-hero-image-container {
        margin-top: 40px;
    }

    .consulting-capabilities-grid,
    .consulting-case-studies-list {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .consulting-container {
        padding: 0 20px;
    }

    .gradient-text {
        font-size: 2.5rem;
    }

    .consulting-section-heading {
        font-size: 2rem;
    }

    .consulting-section-description {
        font-size: 1rem;
    }

    .consulting-hero,
    .consulting-capabilities,
    .consulting-stats,
    .consulting-case-studies,
    .consulting-methodology {
        padding: 60px 0;
    }

    .consulting-stats-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        padding: 30px 20px;
    }

    .methodology-steps {
        flex-direction: column;
        align-items: center;
    }

    .methodology-step {
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    .gradient-text {
        font-size: 2rem;
    }

    .consulting-section-heading {
        font-size: 1.8rem;
    }

    .consulting-hero,
    .consulting-capabilities,
    .consulting-stats,
    .consulting-case-studies,
    .consulting-methodology {
        padding: 40px 0;
    }

    .consulting-capability-item,
    .consulting-stat-item,
    .case-study-content-wrapper {
        padding: 20px;
    }

    .consulting-stat-number {
        font-size: 2.5rem;
    }

    .consulting-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}