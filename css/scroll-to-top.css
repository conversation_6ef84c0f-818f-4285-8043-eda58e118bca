/* Scroll to Top Container */
#scroll-to-top-container {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px; /* Container slightly larger for ring */
    height: 60px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(30px) scale(0.8); /* Start further down and smaller */
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55); /* Bouncier transition */
    cursor: pointer; /* Apply cursor to container */
    border-radius: 50%; /* Needed for potential background clipping if desired */
}

#scroll-to-top-container.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* SVG Progress Ring */
#progress-ring-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: rotate(-90deg); /* Start progress from top */
    z-index: 1; /* Behind the button */
}

#progress-ring-svg circle {
    fill: transparent;
    stroke-width: 3; /* Adjust thickness */
    transition: stroke-dashoffset 0.15s linear; /* Smooth progress update */
}

#progress-ring-bg {
    /* Background track color - subtle */
    stroke: rgba(255, 255, 255, 0.15);
}

#progress-ring-indicator {
    /* Progress indicator color - use accent */
    stroke: var(--accent-blue, #00B0FF);
    stroke-linecap: round; /* Rounded ends */
}

/* Scroll to Top Button Styles (Inside Container) */
#scroll-to-top-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* Center precisely */
    width: 75%; /* Button slightly smaller than container */
    height: 75%;
    border-radius: 50%;
    background: var(--primary-navy, #061035); /* Dark base */
    color: #FFFFFF;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2; /* Above the ring */
    cursor: pointer; /* Still needed on button itself */
    transition: all 0.3s ease-out;
    outline: none;
}

/* Button Icon Styling */
#scroll-to-top-btn svg {
    width: 75%;
    height: 100%;
    display: block;
    transition: transform 0.3s ease-out;
}

/* Stunning Hover/Focus Effects */
#scroll-to-top-container:hover #scroll-to-top-btn,
#scroll-to-top-container:focus-within #scroll-to-top-btn { /* Style button when container has focus */
    background: var(--accent-blue, #00B0FF); /* Bright background */
    transform: translate(-50%, -50%) scale(1.1); /* Scale up button */
    box-shadow: 0 0 20px 5px rgba(0, 176, 255, 0.5); /* Intense glow */
}

#scroll-to-top-container:hover #scroll-to-top-btn svg,
#scroll-to-top-container:focus-within #scroll-to-top-btn svg {
    transform: scale(1.1) translateY(-2px); /* Scale and lift icon */
    filter: brightness(1.2);
}

#scroll-to-top-container:hover #progress-ring-indicator,
#scroll-to-top-container:focus-within #progress-ring-indicator {
    stroke-width: 4; /* Thicken progress ring on hover */
    filter: brightness(1.3);
}

/* Active State */
#scroll-to-top-container:active #scroll-to-top-btn {
    transform: translate(-50%, -50%) scale(1.0); /* Slightly shrink on click */
    box-shadow: 0 3px 10px rgba(0, 176, 255, 0.4); /* Less intense shadow */
    transition-duration: 0.1s;
}

/* Make container focusable for keyboard nav */
#scroll-to-top-container:focus {
    outline: none; /* We use focus-within for styling */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #scroll-to-top-container {
        width: 50px; /* Smaller overall */
        height: 50px;
        bottom: 20px;
        right: 20px;
    }
     #progress-ring-svg circle {
        stroke-width: 2.5; /* Thinner ring */
    }
    #scroll-to-top-btn svg {
        width: 75%;
        height: 75%;
    }

    #scroll-to-top-container:hover #scroll-to-top-btn,
    #scroll-to-top-container:focus-within #scroll-to-top-btn {
        transform: translate(-50%, -50%) scale(1.05); /* Less scale on mobile */
        box-shadow: 0 0 15px 3px rgba(0, 176, 255, 0.5);
    }
     #scroll-to-top-container:hover #progress-ring-indicator,
     #scroll-to-top-container:focus-within #progress-ring-indicator {
        stroke-width: 3;
    }
}