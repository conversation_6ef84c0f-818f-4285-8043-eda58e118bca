:root {
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-blue: #00B0FF;
    --light-blue: #C7E5FF;
    --white: #FFFFFF;
    --off-white: #F4F7FA;
    --text-dark: #333333;
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;
    --shadow-blue: rgba(0, 176, 255, 0.3);
    --section-spacing: 100px;
    --gradient-overlay: linear-gradient(135deg, rgba(6, 16, 53, 0.95) 0%, rgba(10, 29, 69, 0.9) 100%);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
    background-color: var(--white);
    color: var(--text-dark);
}

.container {
    width: 90%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 15px;
    position: relative;
}

/* Typography */
.section-heading {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
}

.section-description {
    text-align: center;
    margin-bottom: 50px;
    font-size: 1.15rem;
    line-height: 1.7;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Section Base Styles */
.section {
    padding: var(--section-spacing) 0;
}

.section-light {
    background-color: var(--white);
    color: var(--text-dark);
}

.section-navy {
    background-color: var(--primary-navy);
    color: var(--text-light);
}

.section-gradient {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--secondary-navy) 50%, var(--deep-navy) 100%);
    color: var(--text-light);
}

/* Card Base Styles */
.card {
    background: var(--white);
    border-radius: 12px;
    padding: 35px;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.card-navy {
    background: var(--primary-navy);
    color: var(--text-light);
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 15px 30px;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--accent-blue);
    color: var(--white);
}

.btn-primary:hover {
    background-color: #0099e6;
    transform: translateY(-2px);
}

.btn-outline {
    border: 2px solid var(--accent-blue);
    color: var(--accent-blue);
}

.btn-outline:hover {
    background-color: var(--accent-blue);
    color: var(--white);
}

/* Grid Layouts */
.grid {
    display: grid;
    gap: 30px;
}

.grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
    grid-template-columns: repeat(4, 1fr);
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 80vh;
    display: flex;
    align-items: center;
    background-size: cover;
    background-position: center;
    padding: 120px 0 80px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-overlay);
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    line-height: 1.2;
    color: var(--white);
    margin-bottom: 30px;
}

.hero-description {
    font-size: 1.25rem;
    color: var(--light-blue);
    margin-bottom: 40px;
    line-height: 1.8;
}

/* Capabilities Section */
.capabilities {
    padding: var(--section-spacing) 0;
    background: var(--off-white);
}

.capabilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.capability-card {
    background: var(--white);
    border-radius: 12px;
    padding: 40px 30px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.capability-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.capability-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 25px;
}

.capability-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--primary-navy);
}

.capability-description {
    color: var(--text-muted);
    line-height: 1.7;
}

/* Data Visualization Section */
.data-section {
    padding: var(--section-spacing) 0;
    background: var(--primary-navy);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.data-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: url('../images/data-pattern.png') no-repeat center right;
    background-size: contain;
    opacity: 0.1;
}

.data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 2;
}

.data-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 35px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.data-metric {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-blue);
    margin-bottom: 15px;
}

.data-label {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.6;
}

/* Case Studies Section */
.case-studies {
    padding: var(--section-spacing) 0;
    background: var(--white);
}

.case-study-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
}

.case-study-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.case-study-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.case-study-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.case-study-content {
    padding: 30px;
    background: var(--white);
}

.case-study-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--primary-navy);
}

.case-study-description {
    color: var(--text-muted);
    margin-bottom: 20px;
    line-height: 1.7;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-title {
        font-size: 3.5rem;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.8rem;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .section-heading {
        font-size: 2.5rem;
    }

    .capability-card, .data-card {
        padding: 30px 25px;
    }

    .data-metric {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2.2rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .section-heading {
        font-size: 2rem;
    }

    .case-study-grid {
        grid-template-columns: 1fr;
    }
}

/* Animation Classes */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.animate-on-scroll.is-visible {
    opacity: 1;
    transform: translateY(0);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 2.5rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 2.5rem; }