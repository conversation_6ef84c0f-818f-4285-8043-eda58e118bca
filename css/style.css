/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    color: #E7E7E7;
    line-height: 1.6;
    overflow-x: hidden;
    background: linear-gradient(135deg, #061035 0%, #0A1D45 50%, #0D2455 100%);
}

/* Main Content */
.main-container,
.contact-container {
    width: 100%;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

.main-container {
    padding-top: 120px;
    padding-bottom: 100px;
}

main {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30px 0;
}

/* Section Layouts */
.section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px 100px;
    margin-bottom: 40px;
}

.section-reverse {
    flex-direction: row-reverse;
}

.section-content {
    width: 48%;
}

.section-image {
    width: 48%;
    height: auto; 
    object-fit: cover;
}

/* Section Typography  */
.section-title {
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #ffffff;
}

.section-text {
    font-size: 18px;
    line-height: 1.7;
    margin-bottom: 20px;
}

/* Innovation Section */
.innovation-section {
    display: flex;
    justify-content: center;
    padding: 40px 100px;
    margin: 80px 0;
}

.innovation-container {
    width: 795px;
    padding: 72px 48px;
    background: linear-gradient(173deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.20) 63%, rgba(0, 0, 0, 0) 100%);
    border-radius: 48px;
    backdrop-filter: blur(8px);
    text-align: center;
}

.innovation-title {
    font-size: 42px;
    font-weight: 600;
    margin-bottom: 30px;
    color: #ffffff;
}

.innovation-text {
    font-size: 18px;
    line-height: 1.7;
    margin-bottom: 20px;
}

/* Contact Form Section */
.contact-form-container {
    display: flex;
    width: min(1196px, 90%);
    height: auto;
    background: rgba(0, 78, 146, 0.05);
    box-shadow: 0px 0px 60px rgba(0, 0, 0, 0.03);
    border-radius: 10px;
    margin: 0 auto;
}

.contact-form {
    flex: 1;
    padding: 50px;
}

.form-row {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.send-button {
    background: black;
    color: white;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 500;
    padding: 15px 48px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.send-button:hover {
    background-color: #333;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffffff;
}

p {
    margin-bottom: 1rem;
}

a {
    color: #C7E5FF;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #ffffff;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #0D2455;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #061035;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 2.5rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 2.5rem; }

/* Responsive Styles */
@media (max-width: 1200px) {
    .contact-form-container {
        width: 90%;
        flex-direction: column;
        height: auto;
    }

    .section {
        padding: 40px 50px;
    }
    
    .section-title {
        font-size: 32px;
    }
    
    .section-text {
        font-size: 17px;
    }
    
    .innovation-title {
        font-size: 36px;
    }
    
    .innovation-text {
        font-size: 17px;
    }
}

@media (max-width: 900px) {
    .section {
        flex-direction: column;
        padding: 30px;
    }

    .section-content, .section-image {
        width: 100%;
        margin-bottom: 30px;
    }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 20px;
    }
    
    .main-container {
        padding-top: 80px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .section-title {
        font-size: 28px;
    }
    
    .section-text {
        font-size: 16px;
    }
    
    .innovation-title {
        font-size: 30px;
    }
    
    .innovation-text {
        font-size: 16px;
    }
}

@media (max-width: 600px) {
    header {
        padding: 10px 20px;
    }

    .section-title {
        font-size: 24px;
    }
    
    .section-text {
        font-size: 14px;
    }
    
    .innovation-title {
        font-size: 24px;
    }
    
    .innovation-text {
        font-size: 14px;
    }
}