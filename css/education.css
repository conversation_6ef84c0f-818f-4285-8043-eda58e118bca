/* Education Page Styling based on Banking Page */
:root {
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-blue: #00B0FF;
    --light-blue: #C7E5FF;
    --white: #FFFFFF;
    --off-white: #F4F7FA;
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;
    --shadow-blue: rgba(0, 176, 255, 0.3);
}


body {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-navy);
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
    scroll-behavior: smooth;
}

.edu-wrapper {
    width: 90%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px; 
}



.edu-hero {
    position: relative;
    text-align: center;
    padding: 150px 0; 
    color: var(--white);
    overflow: hidden;
    perspective: 1000px;
    opacity: 0.3;
    background: url('/images/education-hero.jpg') center/cover no-repeat;
}


@keyframes eduHeroParallax {
    from { transform: translateY(0) rotate(0deg); }
    to { transform: translateY(-50px) rotate(2deg); }
}

.edu-hero-content {
    position: relative;
    z-index: 1;
    transform-style: preserve-3d;
    transform: translateZ(50px);
}

.edu-hero-title {
    font-size: 4.5rem;
    font-weight: 800;
    letter-spacing: -1px;
    margin-bottom: 25px;
    color: var(--white);
}

.edu-hero-subtitle {
    font-size: 1.4rem;
    color: var(--white);
    line-height: 1.9;
    margin-bottom: 40px;
    max-width: 800px;
    margin: 0 auto;
}

/* Industry Overview & General Sections - Navy Background */
.edu-industry-overview,
.edu-case-studies,
.edu-cta-section { 
    padding: 80px 0;
    background-color: var(--deep-navy); 
}

.edu-section {
    margin-bottom: 60px; /* Adjusted margin */
    text-align: center;
    padding: 0 20px; /* Adjusted padding */
}

.edu-section-title {
    font-size: 3rem;
    color: var(--white); /* White section titles */
    font-weight: 700;
    margin-bottom: 30px; /* Adjusted margin */
    position: relative;
    padding-bottom: 15px; /* Adjusted padding */
}

.edu-section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--accent-blue); /* Accent blue underline */
}

.edu-section-text {
    font-size: 1.2rem; /* Slightly reduced font size */
    line-height: 1.8;
    color: var(--text-muted); /* Muted text color */
    margin-bottom: 40px; /* Adjusted margin */
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Stats Section - Glassmorphism Cards */
.edu-stats-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px; /* Adjusted gap */
    margin-top: 50px; /* Adjusted margin */
}

.edu-stat {
    background: rgba(255, 255, 255, 0.1); /* Glassmorphism card background */
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 176, 255, 0.2); /* Accent blue border */
    flex: 1;
    text-align: center;
    padding: 35px 25px; /* Adjusted padding */
    border-radius: 12px; /* Adjusted border-radius */
    min-width: 250px; /* Adjusted min-width */
    transition: transform 0.3s ease, box-shadow 0.3s ease; /* Smooth transitions */
}

.edu-stat:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 20px rgba(0, 176, 255, 0.4); /* Accent blue shadow on hover */
}

.edu-stat-number {
    font-size: 3rem; /* Adjusted font size */
    font-weight: 700;
    color: var(--white); /* White numbers */
    margin-bottom: 10px; /* Adjusted margin */
    text-shadow: 0 3px 8px rgba(0,176,255,0.3); /* Accent blue text shadow */
}

.edu-stat-label {
    font-size: 1rem;
    color: var(--text-muted); /* Muted label text */
    text-transform: uppercase;
    letter-spacing: 0.8px;
}

/* Solutions Grid - Cards with Navy Background and Accent Blue Highlights */
.edu-solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Adjusted minmax */
    gap: 30px; /* Adjusted gap */
    margin-top: 40px; /* Adjusted margin */
}

.edu-solution-card {
    background-color: var(--secondary-navy); /* Secondary navy card background */
    border-radius: 12px; /* Adjusted border-radius */
    padding: 30px; /* Adjusted padding */
    border: 1px solid rgba(0, 176, 255, 0.2); /* Accent blue border */
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2); /* Softer shadow */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    opacity: 1; /* Removed animation for simplicity */
    transform: translateY(0);
}

.edu-solution-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 176, 255, 0.3); /* Accent blue shadow on hover */
}

.edu-solution-title {
    font-size: 1.6rem; /* Adjusted font size */
    color: var(--white); /* White titles */
    margin-bottom: 15px; /* Adjusted margin */
    font-weight: 700;
    position: relative;
}

.edu-solution-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--accent-blue); /* Accent blue underline */
}

.edu-solution-text {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-muted); /* Muted text color */
}

/* Case Studies Section - Cards with White Background and Navy Text */
.edu-case-studies {
    padding: 80px 0;
    background-color: var(--off-white); /* Off-white background for contrast */
}

.edu-case-wrapper {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px; /* Adjusted padding */
}

.edu-case-title {
    font-size: 2.5rem; /* Adjusted font size */
    color: var(--primary-navy); /* Navy title text */
    font-weight: 700;
    margin-bottom: 30px; /* Adjusted margin */
    text-align: center;
}

.edu-case-card {
    background-color: var(--white); /* White case study cards */
    border-radius: 12px; /* Adjusted border-radius */
    padding: 40px; /* Adjusted padding */
    margin-bottom: 30px; /* Adjusted margin */
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); /* Softer shadow */
    border: 1px solid rgba(0, 176, 255, 0.1); /* Light accent blue border */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    opacity: 1; /* Removed animation for simplicity */
    transform: translateX(0);
}

.edu-case-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 176, 255, 0.2); /* Accent blue shadow on hover */
}


.edu-case-heading {
    font-size: 2rem; /* Adjusted font size */
    color: var(--primary-navy); /* Navy heading text */
    margin-bottom: 20px; /* Adjusted margin */
    font-weight: 700;
}

.edu-case-text {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--deep-navy); /* Deep navy text */
    margin-bottom: 25px; /* Adjusted margin */
}

/* CTA Section - Consistent with Banking CTA */
.edu-cta-section {
    background: linear-gradient(135deg, var(--secondary-navy), var(--deep-navy)); /* Banking CTA gradient */
    padding: 80px 0; /* Adjusted padding */
    text-align: center;
    border-top: 1px solid rgba(0, 176, 255, 0.1);
}

.edu-cta-title {
    font-size: 2.8rem; /* Adjusted font size */
    color: var(--white); /* White CTA title */
    margin-bottom: 20px; /* Adjusted margin */
    font-weight: 700;
    line-height: 1.3;
}

.edu-cta-text {
    font-size: 1.2rem;
    color: var(--text-muted); /* Muted CTA text */
    margin-bottom: 30px; /* Adjusted margin */
    line-height: 1.8;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.edu-cta-button {
    display: inline-block;
    padding: 12px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 8px;
    background-color: var(--accent-blue); /* Accent blue button */
    color: var(--deep-navy); /* Deep navy button text */
    text-decoration: none;
    transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 8px 15px rgba(0, 176, 255, 0.3); /* Accent blue shadow */
}

.edu-cta-button:hover {
    background-color: var(--light-blue); /* Light blue on hover */
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 176, 255, 0.5); /* Enhanced shadow on hover */
}


/* Responsive Design - Adjustments for smaller screens */
@media (max-width: 1024px) {
    .edu-hero-title {
        font-size: 3.8rem;
    }
    .edu-section-title {
        font-size: 2.6rem;
    }
    .edu-case-title {
        font-size: 2.3rem;
    }
}

@media (max-width: 768px) {
    .edu-hero {
        padding: 120px 0;
    }
    .edu-hero-title {
        font-size: 3.2rem;
    }
    .edu-hero-subtitle {
        font-size: 1.3rem;
    }
    .edu-section {
        margin-bottom: 50px;
    }
    .edu-section-title {
        font-size: 2.3rem;
    }
    .edu-solutions-grid,
    .edu-stats-container {
        grid-template-columns: 1fr; /* Stack cards on smaller screens */
    }
    .edu-stat,
    .edu-solution-card {
        margin-bottom: 20px;
    }
    .edu-case-card {
        padding: 30px;
    }
}

@media (max-width: 600px) {
    .edu-hero {
        padding: 100px 0;
    }
    .edu-hero-title {
        font-size: 2.8rem;
    }
    .edu-hero-subtitle {
        font-size: 1.2rem;
    }
    .edu-section-title {
        font-size: 2rem;
    }
    .edu-stat-number {
        font-size: 2.5rem;
    }
    .edu-stat-label {
        font-size: 1rem;
    }
    .edu-solution-card,
    .edu-case-card {
        padding: 25px;
    }
    .edu-solution-title {
        font-size: 1.4rem;
    }
    .edu-case-heading {
        font-size: 1.8rem;
    }
    .edu-cta-title {
        font-size: 2.4rem;
    }
    .edu-cta-text {
        font-size: 1.1rem;
    }
    .edu-cta-button {
        font-size: 1rem;
        padding: 10px 25px;
    }
}
