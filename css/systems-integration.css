/* Systems Integration Page Styles */
:root {
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-blue: #00B0FF;
    --light-blue: #C7E5FF;
    --white: #FFFFFF;
    --off-white: #F4F7FA;
    --text-dark: #333333;
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;
    
    /* Shadows */
    --shadow-sm: 0 4px 6px rgba(0, 176, 255, 0.1);
    --shadow-md: 0 8px 15px rgba(0, 176, 255, 0.15);
    --shadow-lg: 0 15px 30px rgba(0, 176, 255, 0.2);
    
    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 2rem;
    --space-lg: 4rem;
    --space-xl: 6rem;
    
    /* Typography */
    --font-size-xs: 0.875rem;
    --font-size-sm: 1rem;
    --font-size-md: 1.25rem;
    --font-size-lg: 1.5rem;
    --font-size-xl: 2rem;
    --font-size-2xl: 2.5rem;
    --font-size-3xl: 3.5rem;
}

/* Base Styles */
.systems-content {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-navy);
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
}

.systems-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 2;
}

/* Hero Section */
.systems-hero {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
    position: relative;
    overflow: hidden;
}

.systems-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(0, 176, 255, 0.1), transparent 40%);
    z-index: 1;
}

.systems-hero-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.systems-hero-content {
    position: relative;
    z-index: 2;
}

.systems-hero-content h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--space-md);
    line-height: 1.2;
    color: var(--white);
}

.systems-hero-content p {
    font-size: var(--font-size-md);
    margin-bottom: var(--space-lg);
    color: var(--text-light);
}

.systems-hero-image-container {
    position: relative;
    z-index: 2;
}

.systems-hero-image {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    transition: transform 0.5s ease;
}

.systems-hero-image:hover {
    transform: translateY(-10px);
}

/* Button Styles */
.systems-btn {
    display: inline-block;
    padding: 12px 30px;
    background: var(--accent-blue);
    color: var(--white);
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.systems-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent-blue), var(--light-blue));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.systems-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.systems-btn:hover::before {
    opacity: 1;
}

.systems-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.systems-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top-color: var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Capabilities Section */
.systems-capabilities {
    padding: var(--space-xl) 0;
    background: var(--secondary-navy);
    position: relative;
}

.systems-capabilities::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 80% 20%, rgba(0, 176, 255, 0.05), transparent 40%);
    z-index: 1;
}

.systems-section-heading {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--space-sm);
    color: var(--white);
    position: relative;
    padding-bottom: var(--space-sm);
}

.systems-section-heading::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-blue), var(--light-blue));
    border-radius: 3px;
}

.systems-section-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto var(--space-lg);
    color: var(--text-muted);
    font-size: var(--font-size-md);
}

.systems-capabilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-md);
    position: relative;
    z-index: 2;
}

.systems-capability-item {
    background: var(--deep-navy);
    padding: var(--space-md);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border-top: 4px solid var(--accent-blue);
    position: relative;
    overflow: hidden;
}

.systems-capability-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.systems-capability-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 176, 255, 0.05) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
}

.systems-capability-item:hover::after {
    opacity: 1;
}

.systems-capability-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    color: var(--white);
    position: relative;
    z-index: 1;
}

.systems-capability-description {
    color: var(--text-muted);
    position: relative;
    z-index: 1;
}

/* Stats Section */
.systems-stats {
    padding: var(--space-xl) 0;
    background: var(--primary-navy);
    position: relative;
}

.systems-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 40%, rgba(0, 176, 255, 0.05), transparent 40%);
    z-index: 1;
}

.systems-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    position: relative;
    z-index: 2;
}

.systems-stat-item {
    background: var(--secondary-navy);
    padding: var(--space-md);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    text-align: center;
    border-bottom: 3px solid var(--accent-blue);
    position: relative;
    overflow: hidden;
}

.systems-stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.systems-stat-item::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 176, 255, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
}

.systems-stat-item:hover::after {
    opacity: 1;
}

.systems-stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--accent-blue);
    margin-bottom: var(--space-xs);
    position: relative;
    z-index: 1;
}

.systems-stat-label {
    color: var(--text-light);
    font-weight: 500;
    position: relative;
    z-index: 1;
}

/* Case Studies Section */
.systems-case-studies {
    padding: var(--space-xl) 0;
    background: var(--secondary-navy);
    position: relative;
}

.systems-case-studies::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 60%, rgba(0, 176, 255, 0.05), transparent 40%);
    z-index: 1;
}

.systems-case-studies-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-md);
    position: relative;
    z-index: 2;
}

.systems-case-study-item {
    background: var(--deep-navy);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
}

.systems-case-study-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.case-study-content-wrapper {
    padding: var(--space-md);
}

.case-study-text {
    position: relative;
    z-index: 1;
}

.case-study-step-number {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--accent-blue);
    opacity: 0.5;
    margin-bottom: var(--space-sm);
    display: block;
}

.case-study-text h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    color: var(--white);
}

.case-study-text p {
    color: var(--text-muted);
    margin-bottom: var(--space-md);
}

.case-study-hidden-content {
    display: none;
}

.case-study-button {
    background: var(--accent-blue);
    color: var(--white);
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.case-study-button:hover {
    background: var(--light-blue);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

/* Technologies Section */
.systems-technologies {
    padding: var(--space-xl) 0;
    background: var(--primary-navy);
    position: relative;
}

.systems-technologies::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 40% 30%, rgba(0, 176, 255, 0.05), transparent 40%);
    z-index: 1;
}

.systems-tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    position: relative;
    z-index: 2;
}

.systems-tech-item {
    background: var(--secondary-navy);
    padding: var(--space-md);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.systems-tech-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.systems-tech-icon {
    fill: var(--accent-blue);
    margin-bottom: var(--space-sm);
    transition: all 0.3s ease;
}

.systems-tech-item:hover .systems-tech-icon {
    transform: scale(1.1);
}

.systems-tech-item p {
    color: var(--text-light);
    font-weight: 500;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(4, 16, 38, 0.8);
    z-index: 1000;
    overflow-y: auto;
}

.modal-content {
    position: relative;
    background: var(--secondary-navy);
    margin: 5% auto;
    padding: var(--space-lg);
    width: 90%;
    max-width: 800px;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    color: var(--text-light);
}

.close-modal {
    position: absolute;
    top: var(--space-sm);
    right: var(--space-sm);
    font-size: 2rem;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-muted);
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--white);
}

.case-study-details {
    margin-top: var(--space-md);
}

.case-study-description {
    margin-bottom: var(--space-md);
    color: var(--text-muted);
}

.case-study-results h3,
.case-study-technologies h3 {
    color: var(--white);
    margin-bottom: var(--space-sm);
    font-size: var(--font-size-lg);
}

.case-study-results ul {
    list-style-type: none;
    padding-left: 0;
    margin-bottom: var(--space-md);
}

.case-study-results li {
    margin-bottom: var(--space-xs);
    padding-left: 20px;
    position: relative;
}

.case-study-results li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-blue);
}

.tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-xs);
}

.tech-tag {
    background: var(--deep-navy);
    color: var(--accent-blue);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: var(--font-size-xs);
    border: 1px solid var(--accent-blue);
}

/* Animation Classes */
.scroll-reveal {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

.floating-element {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .systems-hero-wrapper {
        gap: 40px;
    }
    
    .systems-hero-content h1 {
        font-size: var(--font-size-2xl);
    }
}

@media (max-width: 992px) {
    .systems-container {
        padding: 0 30px;
    }
    
    .systems-capabilities-grid,
    .systems-tech-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .systems-hero {
        padding: 60px 0;
    }
    
    .systems-hero-wrapper {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .systems-hero-content h1 {
        font-size: var(--font-size-xl);
    }
    
    .systems-hero-image-container {
        order: -1;
    }
    
    .systems-capabilities-grid,
    .systems-stats-grid,
    .systems-case-studies-list,
    .systems-tech-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .systems-container {
        padding: 0 20px;
    }
    
    .systems-hero-content h1 {
        font-size: var(--font-size-lg);
    }
    
    .systems-section-heading {
        font-size: var(--font-size-xl);
    }
    
    .systems-btn {
        padding: 10px 20px;
    }
}