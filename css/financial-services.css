
@import url('industry-base.css');

:root {
    /* Main colors - keeping the same color scheme */
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-blue: #00B0FF;
    --light-blue: #C7E5FF;

    /* Using the existing financial services colors but mapping to our navy scheme */
    --saya-financial-primary: var(--primary-navy);
    --saya-financial-secondary: var(--secondary-navy);
    --saya-financial-accent: var(--accent-blue);
    --saya-financial-text: #1A202C;
    --saya-financial-bg: #EDF2F7;
    --saya-light: #FFFFFF;
    --saya-dark: var(--deep-navy);

    /* Text colors */
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;

    /* Spacing */
    --saya-space-unit: 1rem;
    --saya-space-xs: calc(0.25 * var(--saya-space-unit));
    --saya-space-sm: calc(0.5 * var(--saya-space-unit));
    --saya-space-md: var(--saya-space-unit);
    --saya-space-lg: calc(2 * var(--saya-space-unit));
    --saya-space-xl: calc(3 * var(--saya-space-unit));

    /* Typography */
    --saya-text-base: 1rem;
    --saya-text-lg: 1.25rem;
    --saya-text-xl: 1.5rem;
    --saya-text-2xl: 2rem;
    --saya-text-3xl: 3.5rem;

    /* Shadows */
    --saya-shadow-sm: 0 4px 6px rgba(0, 176, 255, 0.1);
    --saya-shadow-md: 0 8px 15px rgba(0, 176, 255, 0.15);
    --saya-shadow-lg: 0 15px 30px rgba(0, 176, 255, 0.2);
    --saya-shadow-xl: 0 20px 40px rgba(0, 176, 255, 0.25);

    /* Transitions */
    --saya-transition-base: all 0.3s ease;
    --saya-transition-smooth: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}


.saya-financial-section {
    --section-padding: var(--saya-space-xl);

    max-width: 1200px;
    margin: 0 auto;
    padding: var(--section-padding) var(--saya-space-md);
}


.saya-financial-hero {
    position: relative;
    background: linear-gradient(135deg, var(--deep-navy) 0%, var(--primary-navy) 100%);
    color: var(--saya-light);
    padding: 120px 0 100px;
    isolation: isolate;
    overflow: hidden;
    min-height: 500px;
    display: flex;
    align-items: center;
}

/* Dynamic background elements */
.saya-financial-hero::before {
    content: '';
    position: absolute;
    top: -10%;
    right: -10%;
    width: 60%;
    height: 70%;
    background: radial-gradient(circle, rgba(0, 176, 255, 0.15) 0%, transparent 70%);
    animation: pulse 8s infinite alternate;
    z-index: 0;
}

.saya-financial-hero::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(rgba(6, 16, 53, 0.85), rgba(10, 29, 69, 0.9));
    z-index: -1;
}

/* Floating particles */
.hero-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: var(--accent-blue);
    border-radius: 50%;
    opacity: 0.4;
    z-index: 1;
}

.hero-particle:nth-child(1) {
    top: 20%;
    left: 10%;
    animation: float 20s infinite ease-in-out;
}

.hero-particle:nth-child(2) {
    top: 60%;
    left: 15%;
    animation: float 15s infinite ease-in-out reverse;
}

.hero-particle:nth-child(3) {
    top: 30%;
    right: 20%;
    animation: float 18s infinite ease-in-out 2s;
}

.hero-particle:nth-child(4) {
    bottom: 20%;
    right: 10%;
    animation: float 12s infinite ease-in-out 1s;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0); }
    25% { transform: translate(20px, 15px); }
    50% { transform: translate(10px, 30px); }
    75% { transform: translate(-10px, 10px); }
}

@keyframes pulse {
    0% { opacity: 0.5; transform: scale(1); }
    100% { opacity: 0.8; transform: scale(1.1); }
}

.saya-financial-hero__content {
    position: relative;
    z-index: 2;
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
    padding: 0 var(--saya-space-md);
}

.saya-financial-hero__title {
    font-size: var(--saya-text-3xl);
    margin-bottom: var(--saya-space-md);
    line-height: 1.2;
    background: linear-gradient(90deg, var(--light-blue), var(--accent-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    text-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    animation: fadeInUp 1s ease-out;
}

.saya-financial-hero__subtitle {
    font-size: var(--saya-text-xl);
    margin-bottom: var(--saya-space-lg);
    opacity: 0.9;
    color: var(--text-light);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    animation: fadeInUp 1s ease-out 0.3s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}



.saya-financial-capabilities {
    background-color: var(--saya-light);
    padding: var(--saya-space-xl) 0;
    position: relative;
    overflow: hidden;
}

/* Add subtle background pattern */
.saya-financial-capabilities::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(0, 176, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(0, 176, 255, 0.03) 0%, transparent 50%);
    z-index: 0;
}

.saya-financial-capabilities__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
    gap: var(--saya-space-lg);
    margin-top: var(--saya-space-lg);
    position: relative;
    z-index: 1;
}

.saya-card {
    background: var(--saya-light);
    border-radius: 12px;
    padding: var(--saya-space-lg);
    transition: var(--saya-transition-smooth);
    box-shadow: var(--saya-shadow-md);
    border-top: 4px solid transparent;
    position: relative;
    overflow: hidden;
}

.saya-card--capability {
    background-color: var(--saya-financial-bg);
    border-color: var(--accent-blue);
}

/* Add subtle accent glow on hover */
.saya-card--capability::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 176, 255, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
}

.saya-card--capability:hover::after {
    opacity: 1;
}

.saya-card--case-study {
    padding: 0;
    overflow: hidden;
    border-top: none;
    border-left: 4px solid var(--accent-blue);
}

.saya-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--saya-shadow-xl);
}

/* Make card content appear above the gradient overlay */
.saya-card__title, .saya-card__description, .saya-card p {
    position: relative;
    z-index: 1;
}

/* Enhanced section headings */
.saya-financial-section h2,
.saya-financial-capabilities h2 {
    color: var(--primary-navy) !important;
    font-size: 2.75em !important;
    font-weight: 700;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    padding-bottom: 15px;
}

/* Add decorative underline to headings */
.saya-financial-section h2::after,
.saya-financial-capabilities h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-blue), var(--light-blue));
    border-radius: 3px;
}

/* Enhanced paragraph styling */
.saya-financial-section p,
.saya-financial-capabilities p {
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    margin-bottom: 20px;
    line-height: 1.7;
    color: var(--saya-financial-text);
}
.saya-card__title {
    color: var(--saya-financial-primary);
    font-size: var(--saya-text-xl);
    margin-bottom: var(--saya-space-sm);
}

.saya-card__description {
    color: var(--saya-financial-text);
    font-size: var(--saya-text-base);
    margin-bottom: var(--saya-space-lg);
}
.saya-card__image {
    height: 200px;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}
.saya-case-studies {
    background-color: var(--saya-financial-bg);
    padding: var(--saya-space-xl) 0;
    position: relative;
    overflow: hidden;
}

/* Add subtle background pattern */
.saya-case-studies::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 70% 20%, rgba(0, 176, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 30% 80%, rgba(0, 176, 255, 0.05) 0%, transparent 50%);
    z-index: 0;
}

.saya-case-study__content {
    padding: var(--saya-space-lg);
    position: relative;
    z-index: 1;
}

.saya-case-study__category {
    color: var(--accent-blue);
    font-weight: 600;
    margin-bottom: var(--saya-space-sm);
}

/* Enhanced case study cards */
.saya-card--case-study {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 20px;
}

.saya-card--case-study .saya-card__title {
    color: var(--primary-navy);
    font-size: 1.5rem;
    margin-bottom: 15px;
    position: relative;
}

.saya-card--case-study p {
    text-align: left;
    color: var(--saya-financial-text);
    margin-bottom: 15px;
    line-height: 1.7;
}


.saya-stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--saya-space-lg);
    margin: var(--saya-space-xl) 0;
}

.saya-stat__item {
    flex: 1;
    min-width: 200px;
    text-align: center;
    padding: var(--saya-space-lg);
    background-color: var(--saya-financial-bg);
    border-radius: 15px;
    box-shadow: var(--saya-shadow-md);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border-bottom: 3px solid var(--accent-blue);
}

.saya-stat__item:hover {
    transform: translateY(-10px);
    box-shadow: var(--saya-shadow-xl);
}

/* Add subtle accent glow */
.saya-stat__item::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 176, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
}

.saya-stat__item:hover::after {
    opacity: 1;
}

.saya-stat__number {
    font-size: var(--saya-text-3xl);
    font-weight: 700;
    color: var(--accent-blue);
    margin-bottom: var(--saya-space-xs);
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 10px rgba(0, 176, 255, 0.2);
}

.saya-stat__label {
    color: var(--saya-financial-text);
    font-size: var(--saya-text-base);
    font-weight: 500;
    position: relative;
    z-index: 1;
}
.saya-financial-cta {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--secondary-navy) 100%);
    color: var(--saya-light);
    padding: var(--saya-space-xl) 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

/* Add dynamic background elements */
.saya-financial-cta::before {
    content: '';
    position: absolute;
    top: -10%;
    left: -10%;
    width: 60%;
    height: 70%;
    background: radial-gradient(circle, rgba(0, 176, 255, 0.1) 0%, transparent 70%);
    animation: pulse 8s infinite alternate;
    z-index: 0;
}

.saya-financial-cta__content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--saya-space-md);
}

.saya-financial-cta h2 {
    color: var(--saya-light) !important;
    margin-bottom: 20px;
}

.saya-financial-cta p {
    color: var(--text-light);
    margin-bottom: 30px;
}

.saya-cta__button {
    display: inline-flex;
    padding: 15px 30px;
    background-color: var(--accent-blue);
    color: var(--saya-light);
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    gap: var(--saya-space-xs);
    align-items: center;
    font-weight: 600;
    font-size: 1.1rem;
    box-shadow: 0 8px 20px rgba(0, 176, 255, 0.3);
}

.saya-cta__button:hover,
.saya-cta__button:focus {
    background-color: var(--light-blue);
    color: var(--primary-navy);
    transform: translateY(-5px);
    box-shadow: 0 12px 25px rgba(0, 176, 255, 0.4);
}


@media (max-width: 768px) {

    :root {
        --saya-text-3xl: 2rem;
        --section-padding: var(--saya-space-lg);
    }

    .saya-financial-capabilities__grid {
        grid-template-columns: 1fr;
    }
}

@media (prefers-reduced-motion: reduce) {
    .saya-card {
        transition: none;
    }
}


@media print {

    .saya-financial-hero {
        background: none !important;
        color: #000 !important;
        padding: 1cm 0;
    }

    .saya-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .saya-cta__button {
        display: none !important;
    }
}
