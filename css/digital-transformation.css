/* Digital Transformation Services Page Styles */
:root {
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-purple: #7C4DFF;
    --light-purple: #B388FF;
    --white: #FFFFFF;
    --off-white: #F4F7FA;
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;
    --shadow-purple: rgba(124, 77, 255, 0.3);
}

/* Global Resets & Base Styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-navy);
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
    overflow-x: hidden;
}

/* Container */
.digital-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 2;
}

/* Hero Section */
.digital-hero {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
    position: relative;
    overflow: hidden;
}

.digital-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(124, 77, 255, 0.1), transparent 40%);
    z-index: 1;
}

.digital-hero-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
}

.digital-hero-content {
    flex: 1;
    max-width: 600px;
    z-index: 2;
}

.digital-hero-image-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

.digital-hero-image {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
}

/* Gradient Text */
.gradient-text {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 24px;
    line-height: 1.2;
    background: linear-gradient(90deg, var(--accent-purple), var(--light-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

/* Button Styles */
.digital-btn {
    display: inline-block;
    padding: 12px 28px;
    background-color: var(--accent-purple);
    color: var(--white);
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 12px var(--shadow-purple);
}

.digital-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px var(--shadow-purple);
}

.digital-hero-btn {
    margin-top: 32px;
}

/* Capabilities Section */
.digital-capabilities {
    padding: 100px 0;
    position: relative;
}

.digital-section-heading {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    color: var(--white);
}

.digital-section-description {
    max-width: 800px;
    margin: 0 auto 60px;
    text-align: center;
    color: var(--text-muted);
    font-size: 1.1rem;
}

.digital-capabilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.digital-capability-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 30px;
    transition: all 0.3s ease;
    border-left: 3px solid var(--accent-purple);
    backdrop-filter: blur(10px);
}

.digital-capability-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.digital-capability-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--accent-purple);
}

.digital-capability-description {
    color: var(--text-light);
    font-size: 1rem;
    line-height: 1.6;
}

/* Stats Section */
.digital-stats {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(6, 16, 53, 0.8), rgba(10, 29, 69, 0.8));
    position: relative;
}

.digital-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.digital-stat-item {
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
    border-bottom: 3px solid var(--accent-purple);
}

.digital-stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.digital-stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-purple);
    margin-bottom: 10px;
}

.digital-stat-label {
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Case Studies Section */
.digital-case-studies {
    padding: 100px 0;
    position: relative;
}

.digital-case-studies-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.digital-case-study-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.digital-case-study-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.case-study-content-wrapper {
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.case-study-text {
    flex: 1;
    padding: 30px;
    position: relative;
}

.case-study-step-number {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-purple);
    opacity: 0.5;
}

.case-study-text h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--white);
}

.case-study-text p {
    color: var(--text-muted);
    margin-bottom: 20px;
}

.case-study-hidden-content {
    display: none;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-top: 20px;
}

.case-study-hidden-content h4 {
    color: var(--accent-purple);
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.case-study-hidden-content p {
    color: var(--text-light);
    margin-bottom: 15px;
}

.case-study-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: transparent;
    color: var(--accent-purple);
    border: 2px solid var(--accent-purple);
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.case-study-button:hover {
    background-color: var(--accent-purple);
    color: var(--white);
}

/* Technologies Section */
.digital-technologies {
    padding: 100px 0;
    position: relative;
}

.digital-tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.digital-tech-item {
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.digital-tech-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
}

.digital-tech-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    fill: var(--accent-purple);
    transition: all 0.3s ease;
}

.digital-tech-item:hover .digital-tech-icon {
    transform: scale(1.1);
}

.digital-tech-item p {
    color: var(--text-light);
    font-size: 1.1rem;
    margin: 0;
}

/* Animations */
.floating-element {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.dynamic-shadow {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.button-hover {
    position: relative;
    overflow: hidden;
}

.button-hover::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.button-hover:hover::after {
    width: 300px;
    height: 300px;
}

/* Scroll Reveal Animation */
.scroll-reveal {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    overflow-y: auto;
}

.modal-content {
    background: var(--primary-navy);
    margin: 50px auto;
    padding: 40px;
    border-radius: 12px;
    max-width: 800px;
    position: relative;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.close-modal {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 2rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: color 0.3s ease;
    background: none;
    border: none;
    padding: 0;
}

.close-modal:hover {
    color: var(--white);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .gradient-text {
        font-size: 3rem;
    }
    .digital-section-heading {
        font-size: 2.2rem;
    }
}

@media (max-width: 992px) {
    .digital-container {
        padding: 0 30px;
    }
    .digital-hero-wrapper {
        flex-direction: column;
    }
    .digital-hero-content {
        text-align: center;
        margin-bottom: 40px;
    }
    .digital-hero-image-container {
        max-width: 80%;
    }
    .digital-capabilities-grid,
    .digital-case-studies-list {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .digital-container {
        padding: 0 20px;
    }
    .gradient-text {
        font-size: 2.5rem;
    }
    .digital-section-heading {
        font-size: 2rem;
    }
    .digital-section-description {
        font-size: 1rem;
    }
    .digital-hero {
        padding: 60px 0;
    }
    .digital-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    .modal-content {
        margin: 20px;
        padding: 20px;
    }
}

@media (max-width: 576px) {
    .gradient-text {
        font-size: 2rem;
    }
    .digital-section-heading {
        font-size: 1.8rem;
    }
    .digital-hero {
        padding: 40px 0;
    }
    .digital-capability-item,
    .digital-case-study-item {
        padding: 20px;
    }
    .digital-stat-number {
        font-size: 2.5rem;
    }
    .digital-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}