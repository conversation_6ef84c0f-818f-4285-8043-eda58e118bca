/* Base Styles for All Service Pages */
:root {
    /* Color Palette */
    --primary-navy: #061035;
    --secondary-navy: #0A1D45;
    --deep-navy: #041026;
    --accent-blue: #00B0FF;
    --light-blue: #C7E5FF;
    --white: #FFFFFF;
    --off-white: #F4F7FA;
    --text-dark: #333333;
    --text-light: #E6E6F2;
    --text-muted: #A0B4D4;
    
    /* Shadows */
    --shadow-sm: 0 4px 6px rgba(0, 176, 255, 0.1);
    --shadow-md: 0 8px 15px rgba(0, 176, 255, 0.15);
    --shadow-lg: 0 15px 30px rgba(0, 176, 255, 0.2);
    
    /* Spacing */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 2rem;
    --space-lg: 4rem;
    --space-xl: 6rem;
    
    /* Typography */
    --font-size-xs: 0.875rem;
    --font-size-sm: 1rem;
    --font-size-md: 1.25rem;
    --font-size-lg: 1.5rem;
    --font-size-xl: 2rem;
    --font-size-2xl: 2.5rem;
    --font-size-3xl: 3.5rem;
}

/* Hero Section */
.service-hero {
    background: linear-gradient(rgba(6, 16, 53, 0.8), rgba(10, 29, 69, 0.8)), var(--hero-bg-image);
    background-size: cover;
    background-position: center;
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    padding: 0 var(--space-md);
    position: relative;
    overflow: hidden;
}

/* Add subtle animated gradient overlay */
.service-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 40%, rgba(0, 176, 255, 0.1) 0%, transparent 70%);
    animation: pulse 8s infinite alternate;
    z-index: 0;
}

@keyframes pulse {
    0% { opacity: 0.5; transform: scale(1); }
    100% { opacity: 0.8; transform: scale(1.1); }
}

.service-hero__content {
    max-width: 800px;
    position: relative;
    z-index: 1;
}

.service-hero__title {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-sm);
    font-weight: 600;
    animation: fadeInUp 1s ease-out;
}

.service-hero__subtitle {
    font-size: var(--font-size-lg);
    font-weight: 300;
    animation: fadeInUp 1s ease-out 0.3s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Section Container */
.service-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-lg) var(--space-md);
}

.service-overview {
    text-align: center;
    margin-bottom: var(--space-lg);
}

.service-overview h2 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-md);
    color: var(--primary-navy);
    position: relative;
    padding-bottom: var(--space-sm);
}

.service-overview h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-blue), var(--light-blue));
    border-radius: 3px;
}

.service-overview p {
    max-width: 800px;
    margin: 0 auto;
    font-size: var(--font-size-md);
    line-height: 1.6;
    color: var(--text-dark);
}

/* Stats Container */
.service-stats-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin-top: var(--space-lg);
    gap: var(--space-md);
}

.service-stat__item {
    text-align: center;
    padding: var(--space-md);
    background-color: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    flex: 1;
    min-width: 200px;
    max-width: 300px;
    border-bottom: 3px solid var(--accent-blue);
    position: relative;
    overflow: hidden;
}

.service-stat__item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.service-stat__item::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 176, 255, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
}

.service-stat__item:hover::after {
    opacity: 1;
}

.service-stat__number {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--accent-blue);
    margin-bottom: var(--space-xs);
    position: relative;
    z-index: 1;
}

.service-stat__label {
    font-size: var(--font-size-sm);
    color: var(--text-dark);
    position: relative;
    z-index: 1;
}

/* Service Cards Grid */
.service-capabilities__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-md);
    margin-top: var(--space-lg);
}

.service-card {
    background: var(--white);
    border-radius: 12px;
    padding: var(--space-md);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border-top: 4px solid var(--accent-blue);
    position: relative;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.service-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 176, 255, 0.05) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
}

.service-card:hover::after {
    opacity: 1;
}

.service-card__title {
    color: var(--primary-navy);
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-sm);
    position: relative;
    z-index: 1;
}

.service-card__description {
    color: var(--text-dark);
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

/* Case Studies Section */
.service-case-studies {
    background-color: var(--off-white);
    padding: var(--space-lg) 0;
    position: relative;
    overflow: hidden;
}

.service-case-studies::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 70% 20%, rgba(0, 176, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 30% 80%, rgba(0, 176, 255, 0.05) 0%, transparent 50%);
    z-index: 0;
}

.service-case-study-card {
    background: var(--white);
    border-radius: 12px;
    margin-bottom: var(--space-md);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border-left: 4px solid var(--accent-blue);
    overflow: hidden;
    position: relative;
}

.service-case-study-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.service-case-study__content {
    padding: var(--space-md);
    position: relative;
    z-index: 1;
}

.service-case-study__title {
    color: var(--primary-navy);
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-sm);
}

.service-case-study__description {
    color: var(--text-dark);
    line-height: 1.6;
}

/* Technologies Section */
.service-technologies {
    background-color: var(--white);
    padding: var(--space-lg) 0;
}

.service-tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-md);
    margin-top: var(--space-md);
}

.service-tech-item {
    text-align: center;
    padding: var(--space-sm);
    transition: transform 0.3s ease;
}

.service-tech-item:hover {
    transform: translateY(-5px);
}

.service-tech-item img,
.service-tech-item svg {
    width: 60px;
    height: 60px;
    margin-bottom: var(--space-sm);
}

.service-tech-item p {
    color: var(--text-dark);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .service-hero__title {
        font-size: var(--font-size-2xl);
    }
    
    .service-hero__subtitle {
        font-size: var(--font-size-md);
    }
    
    .service-overview h2 {
        font-size: var(--font-size-xl);
    }
}

@media (max-width: 992px) {
    .service-section {
        padding: var(--space-md) var(--space-sm);
    }
    
    .service-capabilities__grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .service-tech-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
}

@media (max-width: 768px) {
    .service-hero {
        min-height: 50vh;
    }
    
    .service-hero__title {
        font-size: var(--font-size-xl);
    }
    
    .service-hero__subtitle {
        font-size: var(--font-size-sm);
    }
    
    .service-capabilities__grid {
        grid-template-columns: 1fr;
    }
    
    .service-stats-container {
        flex-direction: column;
        align-items: center;
    }
    
    .service-stat__item {
        width: 100%;
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    .service-hero__title {
        font-size: var(--font-size-lg);
    }
    
    .service-section {
        padding: var(--space-md) var(--space-xs);
    }
    
    .service-overview h2 {
        font-size: var(--font-size-lg);
    }
    
    .service-overview p {
        font-size: var(--font-size-sm);
    }
    
    .service-card {
        padding: var(--space-sm);
    }
    
    .service-card__title {
        font-size: var(--font-size-md);
    }
    
    .service-tech-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
