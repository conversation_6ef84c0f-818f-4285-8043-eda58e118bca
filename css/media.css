/* Import base styles */
@import url('industry-base.css');

:root {
    --saya-media-primary: #2D3748;
    --saya-media-secondary: #4A5568;
    --saya-media-accent: #A0AEC0;
    --saya-media-text: #1A202C;
    --saya-media-bg: #EDF2F7;
    --saya-light: #FFFFFF;
    --saya-dark: #0A1A2F;
    
    --saya-space-unit: 1rem;
    --saya-space-xs: calc(0.25 * var(--saya-space-unit));
    --saya-space-sm: calc(0.5 * var(--saya-space-unit));
    --saya-space-md: var(--saya-space-unit);
    --saya-space-lg: calc(2 * var(--saya-space-unit));
    --saya-space-xl: calc(3 * var(--saya-space-unit));
    
    --saya-text-base: 1rem;
    --saya-text-lg: 1.25rem;
    --saya-text-xl: 1.5rem;
    --saya-text-2xl: 2rem;
    --saya-text-3xl: 3rem;
    
    --saya-shadow-sm: 0 1px 3px rgba(0,0,0,0.12);
    --saya-shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --saya-shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    
    --saya-transition-base: all 0.3s ease;
}

.saya-media-section {
    --section-padding: var(--saya-space-xl);
    
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--section-padding) var(--saya-space-md);
}

.saya-media-hero {
    position: relative;
    background: linear-gradient(135deg, var(--saya-dark) 0%, var(--saya-media-primary) 100%);
    color: var(--saya-light);
    padding: var(--saya-space-xl) 0;
    isolation: isolate;
    overflow: hidden;
}

.saya-media-hero::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(rgba(9, 17, 46, 0.8), rgba(9, 17, 46, 0.9));
    z-index: -1;
}

.saya-media-hero__content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    padding: 0 var(--saya-space-md);
}

.saya-media-hero__title {
    font-size: var(--saya-text-3xl);
    margin-bottom: var(--saya-space-md);
    line-height: 1.2;
}

.saya-media-hero__subtitle {
    font-size: var(--saya-text-lg);
    margin-bottom: var(--saya-space-lg);
    opacity: 0.9;
}

.saya-media-capabilities {
    background-color: var(--saya-light);
    padding: var(--saya-space-xl) 0;
}

.saya-media-capabilities__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
    gap: var(--saya-space-lg);
    margin-top: var(--saya-space-lg);
}

.saya-card {
    background: var(--saya-light);
    border-radius: 8px;
    padding: var(--saya-space-lg);
    transition: var(--saya-transition-base);
    box-shadow: var(--saya-shadow-md);
    border-top: 4px solid transparent;
}

.saya-card--capability {
    background-color: var(--saya-media-bg);
    border-color: var(--saya-media-accent);
}

.saya-card--case-study {
    padding: 0;
    overflow: hidden;
}

.saya-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--saya-shadow-lg);
}

.saya-media-capabilities h2 {
    color: var(--saya-media-primary) !important;
    font-size: 2.75em !important;
    font-weight: 700;
    text-align: center;
    margin-bottom: 50px;
}

.saya-media-section h2 {
    color: var(--saya-media-primary) !important;
    font-size: 2.75em !important;
    font-weight: 700;
    text-align: center;
    margin-bottom: 50px;
}

.saya-card__title {
    color: var(--saya-media-primary);
    font-size: var(--saya-text-xl);
    margin-bottom: var(--saya-space-sm);
}

.saya-card__description {
    color: var(--saya-media-text);
    font-size: var(--saya-text-base);
    margin-bottom: var(--saya-space-lg);
}

.saya-card__image {
    height: 200px;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.saya-case-studies {
    background-color: var(--saya-media-bg);
    padding: var(--saya-space-xl) 0;
}

.saya-case-study__content {
    padding: var(--saya-space-lg);
}

.saya-case-study__category {
    color: var(--saya-media-secondary);
    font-weight: 600;
    margin-bottom: var(--saya-space-sm);
}

.saya-stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--saya-space-lg);
    margin: var(--saya-space-xl) 0;
}

.saya-stat__number {
    font-size: var(--saya-text-3xl);
    font-weight: 700;
    color: var(--saya-media-primary);
    margin-bottom: var(--saya-space-xs);
}

.saya-stat__label {
    color: var(--saya-media-text);
    font-size: var(--saya-text-base);
}

.saya-stat__item {
    flex: 1;
    min-width: 200px;
    text-align: center;
    padding: var(--saya-space-lg);
    background-color: var(--saya-media-bg);
    border-radius: 10px;
    box-shadow: var(--saya-shadow-md);
    transition: transform var(--saya-transition-base);
}

.saya-media-cta {
    background-color: var(--saya-media-primary);
    color: var(--saya-light);
    padding: var(--saya-space-xl) 0;
    text-align: center;
}

.saya-cta__button {
    display: inline-flex;
    padding: var(--saya-space-sm) var(--saya-space-lg);
    background-color: var(--saya-media-secondary);
    color: var(--saya-light);
    border-radius: 4px;
    text-decoration: none;
    transition: background-color 0.3s ease;
    gap: var(--saya-space-xs);
    align-items: center;
}

.saya-cta__button:hover,
.saya-cta__button:focus {
    background-color: var(--saya-media-accent);
    outline: 2px solid var(--saya-light);
    outline-offset: 2px;
}

@media (max-width: 768px) {
    :root {
        --saya-text-3xl: 2rem;
        --section-padding: var(--saya-space-lg);
    }
    
    .saya-media-capabilities__grid {
        grid-template-columns: 1fr;
    }
}

@media (prefers-reduced-motion: reduce) {
    .saya-card {
        transition: none;
    }
}

@media print {
    .saya-media-hero {
        background: none !important;
        color: #000 !important;
        padding: 1cm 0;
    }
    
    .saya-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    .saya-cta__button {
        display: none !important;
    }
}
