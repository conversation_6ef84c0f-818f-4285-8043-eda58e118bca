<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Base URL for all relative paths -->
    <base href="/">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <meta name="title" content="Saya Setona | Digital Banking Solutions">
    <meta name="description" content="Saya Setona provides innovative digital banking solutions, including core banking systems, payment integrations, and risk management.">
    <meta name="keywords" content="digital banking, core banking, payments, risk management, banking solutions">
    <meta name="author" content="Saya Setona">
    <meta name="robots" content="index, follow">

    <meta property="og:title" content="Saya Setona | Digital Banking Solutions">
    <meta property="og:description" content="Innovative digital banking solutions for modern financial institutions.">
    <meta property="og:url" content="https://www.saya-setona.co.za/pages/banking.html">
    <meta property="og:type" content="website">
    <meta property="og:image" content="https://www.saya-setona.co.za/images/banking-hero.jpg">

    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Saya Setona | Digital Banking Solutions">
    <meta name="twitter:description" content="Innovative digital banking solutions for modern financial institutions.">
    <meta name="twitter:image" content="https://www.saya-setona.co.za/images/banking-hero.jpg">

    <title>Saya Setona | Digital Banking Solutions</title>
    <link rel="icon" type="image/png" href="images/logo.png">

    <link rel="stylesheet" href="css/navbar.css">
    <link rel="stylesheet" href="css/footer.css">
    <link rel="stylesheet" href="css/banking.css">
    <link rel="stylesheet" href="css/scroll-to-top.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">


</head>
<body>
    <div id="navbar-container"></div>

    <main class="banking-content">
        <section class="banking-hero">
            <div class="banking-container">
                <div class="banking-hero-wrapper">
                    <div class="banking-hero-content">
                        <h1 class="gradient-text">Digital Banking Solutions</h1>
                        <p>Introducing modern digital channels and platforms to help banks reach further and offer more. We work closely with financial institutions to drive innovation, enhance customer experience, and navigate complex regulatory landscapes.</p>
                        <a href="#capabilities" class="banking-btn banking-hero-btn button-hover">Explore Capabilities</a>
                    </div>
                    <div class="banking-hero-image-container floating-element">
                        <img src="images/banking-hero.png" alt="Digital Banking Solutions" class="banking-hero-image dynamic-shadow" loading="lazy">
                    </div>
                </div>
            </div>
        </section>

        <section id="capabilities" class="banking-capabilities">
            <div class="banking-container">
                <h2 class="banking-section-heading scroll-reveal">Our Core Capabilities</h2>
                <p class="banking-section-description scroll-reveal">We provide comprehensive digital transformation solutions for the banking sector, addressing key challenges and opportunities across various domains of financial services.</p>
                <div class="banking-capabilities-grid">
                    <div class="banking-capability-item scroll-reveal">
                        <div class="banking-capability-content">
                            <h3>Core Banking</h3>
                            <p>Modernizing core banking systems for increased efficiency and agility.</p>
                        </div>
                    </div>
                    <div class="banking-capability-item scroll-reveal">
                        <div class="banking-capability-content">
                            <h3>Digital Channels</h3>
                            <p>Creating seamless omnichannel experiences for customers.</p>
                        </div>
                    </div>
                    <div class="banking-capability-item scroll-reveal">
                        <div class="banking-capability-content">
                            <h3>Payments</h3>
                            <p>Implementing innovative payment solutions and integrations.</p>
                        </div>
                    </div>
                    <div class="banking-capability-item scroll-reveal">
                        <div class="banking-capability-content">
                            <h3>Risk & Compliance</h3>
                            <p>Ensuring robust risk management and regulatory compliance.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>



        <section class="banking-case-studies">
            <div class="banking-container">
                <h2 class="banking-section-heading scroll-reveal">Success Stories</h2>
                <div class="banking-case-studies-list">
                    <!-- Case Study 1 -->
                    <div class="banking-case-study-item hover-3d scroll-reveal">
                        <div class="case-study-content-wrapper">
                            <div class="case-study-text">
                                <span class="case-study-step-number">01</span>
                                <h3>Cloud Infrastructure Modernization</h3>
                                <p>Transforming cloud infrastructure and payment systems for enhanced performance and scalability.</p>
                                <div class="case-study-hidden-content">
                                    <h4>Challenge</h4>
                                    <p>Modernize legacy systems while maintaining 24/7 banking operations. The existing infrastructure struggled with peak loads and deploying new features was slow and risky.</p>
                                    <h4>Solution</h4>
                                    <p>Implemented a scalable, cloud-native architecture using microservices. Employed a blue-green deployment strategy for zero-downtime migration and updates.</p>
                                    <h4>Results</h4>
                                    <p>Achieved 40% faster transaction processing times, 99.99% uptime, and reduced infrastructure costs by 25%. Feature deployment cycles shortened from weeks to days.</p>
                                </div>
                                <button class="case-study-button button-hover" onclick="openModal(this)">Read Case Study</button>
                            </div>
                        </div>
                    </div>

                    <!-- Case Study 2 -->
                    <div class="banking-case-study-item hover-3d scroll-reveal">
                        <div class="case-study-content-wrapper">
                            <div class="case-study-text">
                                <span class="case-study-step-number">02</span>
                                <h3>Digital Banking Channels</h3>
                                <p>Developing cutting-edge digital platforms for seamless and engaging banking experiences.</p>
                                <div class="case-study-hidden-content">
                                    <h4>Challenge</h4>
                                    <p>Unify disparate web and mobile channels into a cohesive, user-friendly omnichannel experience. Improve customer onboarding and engagement metrics.</p>
                                    <h4>Solution</h4>
                                    <p>Created a unified front-end platform with shared components and APIs. Integrated AI-powered personalization for recommendations and support.</p>
                                    <h4>Results</h4>
                                    <p>35% increase in digital channel engagement, 50% faster customer onboarding time, and significantly higher customer satisfaction scores reported via app reviews.</p>
                                </div>
                                <button class="case-study-button button-hover" onclick="openModal(this)">Read Case Study</button>
                            </div>
                        </div>
                    </div>

                    <!-- Case Study 3 -->
                     <div class="banking-case-study-item hover-3d scroll-reveal">
                        <div class="case-study-content-wrapper">
                            <div class="case-study-text">
                                <span class="case-study-step-number">03</span>
                                 <h3>Mobile Banking Innovation</h3>
                                 <p>Driving mobile-first banking solutions to enhance customer accessibility and convenience.</p>
                                <div class="case-study-hidden-content">
                                    <h4>Challenge</h4>
                                    <p>Enhance the existing mobile banking app experience for a diverse user base, including low-bandwidth environments. Introduce new, innovative features securely.</p>
                                    <h4>Solution</h4>
                                    <p>Developed a redesigned, intuitive mobile app focusing on performance and offline capabilities. Implemented biometric authentication and in-app chat support.</p>
                                    <h4>Results</h4>
                                    <p>60% year-over-year growth in active mobile transaction users. App store ratings improved significantly, reflecting higher customer satisfaction and ease of use.</p>
                                 </div>
                                 <button class="case-study-button button-hover" onclick="openModal(this)">Read Case Study</button>
                            </div>
                        </div>
                    </div>

                    <!-- Case Study 4 -->
                    <div class="banking-case-study-item hover-3d scroll-reveal">
                         <div class="case-study-content-wrapper">
                            <div class="case-study-text">
                                <span class="case-study-step-number">04</span>
                                 <h3>Banking Data Analytics Platform</h3>
                                 <p>Leveraging data analytics to improve customer insights and personalize financial services.</p>
                                 <div class="case-study-hidden-content">
                                    <h4>Challenge</h4>
                                    <p>Unlock actionable insights from vast amounts of customer transaction and interaction data silos to drive business growth and improve risk management.</p>
                                    <h4>Solution</h4>
                                    <p>Implemented a centralized data lake and advanced analytics platform. Developed machine learning models for customer segmentation, churn prediction, and fraud detection.</p>
                                    <h4>Results</h4>
                                    <p>20% measured increase in customer lifetime value through targeted offers. Improved credit risk assessment accuracy by 15% and reduced fraudulent transaction losses.</p>
                                 </div>
                                 <button class="case-study-button button-hover" onclick="openModal(this)">Read Case Study</button>
                            </div>
                         </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="banking-challenges scroll-reveal">
            <div class="banking-container">
                <div class="challenges-content glass-container">
                    <h2>Navigating Banking Evolution</h2>
                    <p>We work with banks and payment providers to adapt and prepare for market changes, increasing competition, and shifting regulations. As technology evolves, we help banks keep up with the rapid changes in internal processes, technical solutions and customer needs.</p>
                </div>
            </div>
        </section>

        <section class="banking-cta scroll-reveal">
            <div class="banking-container">
                <div class="cta-content">
                    <h2>Start the Conversation</h2>
                    <p>Let’s reshape what’s possible.</p>
                    <a href="/pages/contact" class="cta-button button-hover cta-button-animated">Contact us</a>
                </div>
            </div>
        </section>

        <!-- Modal Structure -->
        <div id="caseStudyModal" class="modal">
            <div class="modal-content">
                <span class="close">×</span>
                <div id="modalContent">
                </div>
            </div>
        </div>
    </main>

    <div id="footer-container"></div>

    <script src="js/components/absolute-navbar.js"></script>
    <script src="js/components/footer.js"></script>
    <script src="js/components/scroll-to-top.js"></script>

    <!-- Page Specific Script -->
    <script>
        const modal = document.getElementById('caseStudyModal');
        const modalContentEl = document.getElementById('modalContent');
        const closeModalBtn = modal ? modal.querySelector('.close') : null;

        function openModal(button) {
            if (!modal || !modalContentEl) {
                console.error("Modal elements not found!");
                return;
            }
            const textContainer = button.closest('.case-study-text');
            if (!textContainer) {
                console.error("Could not find '.case-study-text' parent for the button.");
                return;
            }
            const hiddenContentDiv = textContainer.querySelector('.case-study-hidden-content');
            if (!hiddenContentDiv) {
                 console.error("Could not find '.case-study-hidden-content' within the text container.");
                return;
            }

            const hiddenContentHTML = hiddenContentDiv.innerHTML;
            modalContentEl.innerHTML = hiddenContentHTML;

            modal.style.display = 'flex';


            setTimeout(() => {
                modal.classList.add('modal-visible');
            }, 10);


            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            if (!modal) return;

            modal.classList.remove('modal-visible');


            const transitionEndHandler = (event) => {

                 if (event.target === modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = '';
                    modal.removeEventListener('transitionend', transitionEndHandler);
                 }
            };
            modal.addEventListener('transitionend', transitionEndHandler);

            setTimeout(() => {
                 if (modal.style.display !== 'none' && !modal.classList.contains('modal-visible')) {
                      console.warn("Transitionend event fallback triggered for modal close.");
                      modal.style.display = 'none';
                      document.body.style.overflow = '';
                 }
            }, 500);
        }

        // --- Event Listeners ---

        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', closeModal);
        } else if (modal) { // Only log error if modal exists but button doesn't
            console.error("Modal close button (.close) not found inside #caseStudyModal.");
        }

        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                closeModal();
            }
        });

        window.addEventListener('keydown', (event) => {
            if (modal && modal.classList.contains('modal-visible') && event.key === 'Escape') {
                closeModal();
            }
        });



        document.addEventListener('DOMContentLoaded', () => {
            const scrollRevealElements = document.querySelectorAll('.scroll-reveal');
            if (scrollRevealElements.length > 0) {

                scrollRevealElements.forEach(element => {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(30px)';
                });

                const revealOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const revealObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.animation = 'slideUp 0.8s ease-out forwards';

                        } else {

                             if (entry.target.style.animation) {
                                 entry.target.style.animation = 'none';
                                 entry.target.style.opacity = '0';
                                 entry.target.style.transform = 'translateY(30px)';
                             }
                        }
                    });
                }, revealOptions);

                scrollRevealElements.forEach(element => {
                    revealObserver.observe(element);
                });
            } else {
                console.warn("No elements with class 'scroll-reveal' found for animation.");
            }



        });



    </script>
</body>
</html>