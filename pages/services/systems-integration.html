<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Base URL for all relative paths -->
    <base href="/">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <meta name="title" content="Saya Setona | Systems Integration Services">
    <meta name="description" content="Saya Setona provides comprehensive systems integration services to help organizations connect disparate systems, streamline operations, and achieve seamless digital workflows.">
    <meta name="keywords" content="systems integration, enterprise integration, API integration, middleware solutions, data integration, application integration, legacy system integration, ERP integration">
    <meta name="author" content="Saya Setona">
    <meta name="robots" content="index, follow">

    <meta property="og:title" content="Saya Setona | Systems Integration Services">
    <meta property="og:description" content="Expert systems integration solutions to help businesses achieve seamless connectivity and operational efficiency.">
    <meta property="og:url" content="https://www.saya-setona.co.za/services/systems-integration">
    <meta property="og:type" content="website">
    <meta property="og:image" content="https://www.saya-setona.co.za/images/systems-hero.jpg">

    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Saya Setona | Systems Integration Services">
    <meta name="twitter:description" content="Expert systems integration solutions to help businesses achieve seamless connectivity and operational efficiency.">
    <meta name="twitter:image" content="https://www.saya-setona.co.za/images/systems-hero.jpg">

    <title>Saya Setona | Systems Integration Services</title>
    <link rel="icon" type="image/png" href="images/logo.png">

    <link rel="stylesheet" href="css/navbar.css">
    <link rel="stylesheet" href="css/footer.css">
    <link rel="stylesheet" href="css/systems-integration.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .systems-content {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 0;
        }

        .systems-container {
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 40px;
            box-sizing: border-box;
        }
    </style>
    <link rel="stylesheet" href="../../css/systems-integration.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <div id="navbar-container"></div>

    <main class="systems-content">
        <section class="systems-hero">
            <div class="systems-container">
                <div class="systems-hero-wrapper">
                    <div class="systems-hero-content">
                        <h1 class="gradient-text">Systems Integration</h1>
                        <p>Connect, streamline, and optimize your enterprise systems with our comprehensive integration solutions that drive efficiency and innovation.</p>
                        <a href="#capabilities" class="systems-btn systems-hero-btn button-hover">Explore Solutions</a>
                    </div>
                    <div class="systems-hero-image-container floating-element">
                        <img src="../../images/systems-hero.png" alt="Systems Integration" class="systems-hero-image dynamic-shadow" loading="lazy">
                    </div>
                </div>
            </div>
        </section>

        <section id="capabilities" class="systems-capabilities">
            <div class="systems-container">
                <h2 class="systems-section-heading scroll-reveal">Our Integration Solutions</h2>
                <p class="systems-section-description scroll-reveal">We provide comprehensive systems integration solutions to help businesses achieve seamless connectivity and operational efficiency.</p>

                <div class="systems-capabilities-grid">
                    <div class="systems-capability-item scroll-reveal">
                        <h3 class="systems-capability-title">Enterprise Application Integration</h3>
                        <p class="systems-capability-description">Connect and synchronize enterprise applications to create seamless business processes and workflows.</p>
                    </div>
                    <div class="systems-capability-item scroll-reveal">
                        <h3 class="systems-capability-title">API Management</h3>
                        <p class="systems-capability-description">Design, implement, and manage APIs to enable secure and efficient data exchange between systems.</p>
                    </div>
                    <div class="systems-capability-item scroll-reveal">
                        <h3 class="systems-capability-title">Data Integration</h3>
                        <p class="systems-capability-description">Consolidate and synchronize data across multiple systems to ensure consistency and accessibility.</p>
                    </div>
                    <div class="systems-capability-item scroll-reveal">
                        <h3 class="systems-capability-title">Legacy System Integration</h3>
                        <p class="systems-capability-description">Connect legacy systems with modern applications while maintaining data integrity and business continuity.</p>
                    </div>
                    <div class="systems-capability-item scroll-reveal">
                        <h3 class="systems-capability-title">Cloud Integration</h3>
                        <p class="systems-capability-description">Seamlessly integrate cloud and on-premises applications for hybrid enterprise environments.</p>
                    </div>
                    <div class="systems-capability-item scroll-reveal">
                        <h3 class="systems-capability-title">Integration Architecture</h3>
                        <p class="systems-capability-description">Design scalable and maintainable integration architectures aligned with business needs.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="systems-stats">
            <div class="systems-container">
                <h2 class="systems-section-heading scroll-reveal">Integration Impact</h2>
                <p class="systems-section-description scroll-reveal">Our systems integration solutions deliver measurable results for businesses across industries.</p>

                <div class="systems-stats-grid">
                    <div class="systems-stat-item scroll-reveal">
                        <div class="systems-stat-number">150+</div>
                        <div class="systems-stat-label">Integration Projects</div>
                    </div>
                    <div class="systems-stat-item scroll-reveal">
                        <div class="systems-stat-number">45%</div>
                        <div class="systems-stat-label">Efficiency Improvement</div>
                    </div>
                    <div class="systems-stat-item scroll-reveal">
                        <div class="systems-stat-number">99.9%</div>
                        <div class="systems-stat-label">System Uptime</div>
                    </div>
                    <div class="systems-stat-item scroll-reveal">
                        <div class="systems-stat-number">60%</div>
                        <div class="systems-stat-label">Cost Reduction</div>
                    </div>
                </div>
            </div>
        </section>

        <section class="systems-case-studies">
            <div class="systems-container">
                <h2 class="systems-section-heading scroll-reveal">Success Stories</h2>
                <p class="systems-section-description scroll-reveal">See how our systems integration solutions have revolutionized businesses and delivered exceptional results.</p>

                <div class="systems-case-studies-list">
                    <div class="systems-case-study-item hover-3d scroll-reveal">
                        <div class="case-study-content-wrapper">
                            <div class="case-study-text">
                                <span class="case-study-step-number">01</span>
                                <h3>Enterprise Systems Modernization</h3>
                                <p>Integrated legacy systems with modern cloud applications for a financial services provider, resulting in 50% faster processing times and improved data accuracy.</p>
                                <div class="case-study-hidden-content">
                                    <h4>Challenge</h4>
                                    <p>A financial services provider was struggling with legacy systems that were slow, inefficient, and difficult to maintain.</p>
                                    <h4>Solution</h4>
                                    <p>We implemented a comprehensive integration strategy, connecting legacy systems with modern cloud applications through secure APIs and middleware.</p>
                                    <h4>Results</h4>
                                    <p>50% faster processing times, 99.9% data accuracy, and significant reduction in maintenance costs.</p>
                                </div>
                                <button class="case-study-button button-hover" onclick="openModal(this)">Read Case Study</button>
                            </div>
                        </div>
                    </div>

                    <div class="systems-case-study-item hover-3d scroll-reveal">
                        <div class="case-study-content-wrapper">
                            <div class="case-study-text">
                                <span class="case-study-step-number">02</span>
                                <h3>Supply Chain Integration</h3>
                                <p>Implemented end-to-end supply chain integration for a manufacturing company, achieving 40% reduction in order processing time and real-time inventory visibility.</p>
                                <div class="case-study-hidden-content">
                                    <h4>Challenge</h4>
                                    <p>A manufacturing company needed to integrate their supply chain systems to improve visibility and efficiency.</p>
                                    <h4>Solution</h4>
                                    <p>We developed a comprehensive integration platform connecting ERP, inventory, and logistics systems with real-time data synchronization.</p>
                                    <h4>Results</h4>
                                    <p>40% reduction in order processing time, real-time inventory visibility, and improved supplier collaboration.</p>
                                </div>
                                <button class="case-study-button button-hover" onclick="openModal(this)">Read Case Study</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="systems-technologies">
            <div class="systems-container">
                <h2 class="systems-section-heading scroll-reveal">Integration Technologies</h2>
                <p class="systems-section-description scroll-reveal">We leverage cutting-edge technologies to drive successful systems integration.</p>

                <div class="systems-tech-grid">
                    <div class="systems-tech-item scroll-reveal">
                        <svg class="systems-tech-icon" width="60" height="60" viewBox="0 0 24 24">
                            <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
                        </svg>
                        <p>API Management</p>
                    </div>
                    <div class="systems-tech-item scroll-reveal">
                        <svg class="systems-tech-icon" width="60" height="60" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                        </svg>
                        <p>Middleware Solutions</p>
                    </div>
                    <div class="systems-tech-item scroll-reveal">
                        <svg class="systems-tech-icon" width="60" height="60" viewBox="0 0 24 24">
                            <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM19 18H6c-2.21 0-4-1.79-4-4 0-2.05 1.53-3.76 3.56-3.97l1.07-.11.5-.95C8.08 7.14 9.94 6 12 6c2.62 0 4.88 1.86 5.39 4.43l.3 1.5 1.53.11c1.56.1 2.78 1.41 2.78 2.96 0 1.65-1.35 3-3 3z"/>
                        </svg>
                        <p>Data Integration</p>
                    </div>
                    <div class="systems-tech-item scroll-reveal">
                        <svg class="systems-tech-icon" width="60" height="60" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                        </svg>
                        <p>Cloud Integration</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modal for Case Studies -->
    <div id="caseStudyModal" class="modal">
        <div class="modal-content">
            <button class="close-modal" onclick="closeModal()">&times;</button>
            <div id="modalContent"></div>
        </div>
    </div>

    <script src="js/components/absolute-navbar.js"></script>
    <script src="js/components/footer.js"></script>
    <script src="js/components/scroll-to-top.js"></script>

    <script>
        // Scroll Reveal Animation
        document.addEventListener('DOMContentLoaded', function() {
            const revealElements = document.querySelectorAll('.scroll-reveal');

            function checkReveal() {
                revealElements.forEach(element => {
                    const elementTop = element.getBoundingClientRect().top;
                    const windowHeight = window.innerHeight;

                    if (elementTop < windowHeight - 100) {
                        element.classList.add('revealed');
                    }
                });
            }

            // Initial check
            checkReveal();

            // Check on scroll
            window.addEventListener('scroll', checkReveal);
        });

        // Modal Functions
        function openModal(button) {
            const modal = document.getElementById('caseStudyModal');
            const modalContent = document.getElementById('modalContent');
            const caseStudy = button.closest('.case-study-text');
            const title = caseStudy.querySelector('h3').textContent;
            const content = caseStudy.querySelector('.case-study-hidden-content').innerHTML;

            modalContent.innerHTML = `<h2>${title}</h2>${content}`;
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            const modal = document.getElementById('caseStudyModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('caseStudyModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>