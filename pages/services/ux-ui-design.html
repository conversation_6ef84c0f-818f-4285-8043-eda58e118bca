<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Base URL for all relative paths -->
    <base href="/">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="title" content="Saya Setona | UX/UI Design Services">
    <meta name="description" content="Saya Setona delivers exceptional user experience and interface design services that create intuitive, engaging, and user-centered digital solutions for modern businesses.">
    <meta name="keywords" content="UX design, UI design, user interface, user experience, digital design, interaction design, visual design, product design, web design, mobile design">
    <meta name="author" content="Saya Setona">
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="Saya Setona | UX/UI Design Services">
    <meta property="og:description" content="Create exceptional digital experiences with our expert UX/UI design services that put users first.">
    <meta property="og:url" content="https://www.saya-setona.co.za/services/ux-ui-design">
    <meta property="og:type" content="website">

    <title>Say<PERSON> | UX/UI Design Services</title>
    <link rel="icon" type="image/png" href="images/logo.png">
    <link rel="stylesheet" href="css/navbar.css">
    <link rel="stylesheet" href="css/footer.css">
    <link rel="stylesheet" href="css/ux-ui-design.css">
    <link rel="stylesheet" href="css/scroll-to-top.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .systems-content {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 0;
        }

        .systems-container {
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 40px;
            box-sizing: border-box;
        }
    </style>
</head>

<body>
    <div class="background-shape"></div>

    <div id="navbar-container"></div>

    <main class="ux-content">
        <section class="ux-hero">
            <div class="ux-container">
                <div class="ux-hero-wrapper">
                    <div class="ux-hero-content">
                        <h1 class="ux-hero-title">UX/UI Design Services</h1>
                        <p class="ux-hero-subtitle">Creating exceptional digital experiences that users love</p>
                        <a href="#contact" class="ux-btn">Get Started</a>
                    </div>
                    <div class="ux-hero-image-container">
                        <img src="../../images/ux-ui-hero-bg.jpeg" alt="UX/UI Design Services" class="ux-hero-image floating-element">
                    </div>
                </div>
            </div>
        </section>

        <div class="ux-container">
            <section class="ux-overview">
                <h2 class="ux-section-heading">User-Centered Design Excellence</h2>
                <p class="ux-section-description">We combine creativity with research-driven insights to design digital experiences that delight users and drive business success through intuitive, engaging interfaces.</p>

                <div class="ux-stats-grid">
                    <div class="ux-stat-item scroll-reveal">
                        <div class="ux-stat-number">250+</div>
                        <div class="ux-stat-label">Projects Delivered</div>
                    </div>
                    <div class="ux-stat-item scroll-reveal">
                        <div class="ux-stat-number">95%</div>
                        <div class="ux-stat-label">User Satisfaction</div>
                    </div>
                    <div class="ux-stat-item scroll-reveal">
                        <div class="ux-stat-number">40%</div>
                        <div class="ux-stat-label">Conversion Increase</div>
                    </div>
                    <div class="ux-stat-item scroll-reveal">
                        <div class="ux-stat-number">85%</div>
                        <div class="ux-stat-label">Reduced Bounce Rate</div>
                    </div>
                </div>
            </section>

            <section class="ux-capabilities">
                <h2 class="ux-section-heading">Our Design Solutions</h2>
                <p class="ux-section-description">Comprehensive UX/UI design services tailored to your business needs</p>

                <div class="ux-capabilities-grid">
                    <div class="ux-capability-item scroll-reveal">
                        <div class="ux-capability-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                            </svg>
                        </div>
                        <h3 class="ux-capability-title">User Experience (UX) Design</h3>
                        <p class="ux-capability-description">Create seamless user journeys through comprehensive research, wireframing, and prototyping.</p>
                    </div>
                    <div class="ux-capability-item scroll-reveal">
                        <div class="ux-capability-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="3" y1="9" x2="21" y2="9"></line>
                                <line x1="9" y1="21" x2="9" y2="9"></line>
                            </svg>
                        </div>
                        <h3 class="ux-capability-title">User Interface (UI) Design</h3>
                        <p class="ux-capability-description">Design visually appealing interfaces that align with brand identity and enhance user engagement.</p>
                    </div>
                    <div class="ux-capability-item scroll-reveal">
                        <div class="ux-capability-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                                <line x1="9" y1="9" x2="9.01" y2="9"></line>
                                <line x1="15" y1="9" x2="15.01" y2="9"></line>
                            </svg>
                        </div>
                        <h3 class="ux-capability-title">Interaction Design</h3>
                        <p class="ux-capability-description">Develop intuitive interaction patterns and micro-animations that guide users effectively.</p>
                    </div>
                    <div class="ux-capability-item scroll-reveal">
                        <div class="ux-capability-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <h3 class="ux-capability-title">User Research</h3>
                        <p class="ux-capability-description">Conduct comprehensive user research to inform design decisions and validate solutions.</p>
                    </div>
                    <div class="ux-capability-item scroll-reveal">
                        <div class="ux-capability-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                        </div>
                        <h3 class="ux-capability-title">Design Systems</h3>
                        <p class="ux-capability-description">Create scalable design systems that ensure consistency across digital products.</p>
                    </div>
                    <div class="ux-capability-item scroll-reveal">
                        <div class="ux-capability-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                        </div>
                        <h3 class="ux-capability-title">Usability Testing</h3>
                        <p class="ux-capability-description">Perform thorough usability testing to optimize user experiences and interfaces.</p>
                    </div>
                </div>
            </section>

            <section class="ux-process">
                <h2 class="ux-section-heading">Our Design Process</h2>
                <p class="ux-section-description">A proven methodology that delivers exceptional results</p>

                <div class="ux-process-steps">
                    <div class="ux-process-step scroll-reveal">
                        <div class="ux-process-step-number">01</div>
                        <h3 class="ux-process-step-title">Discovery & Research</h3>
                        <p class="ux-process-step-description">We begin by understanding your business goals, target audience, and competitive landscape through comprehensive research and stakeholder interviews.</p>
                    </div>

                    <div class="ux-process-step scroll-reveal">
                        <div class="ux-process-step-number">02</div>
                        <h3 class="ux-process-step-title">User Personas & Journey Mapping</h3>
                        <p class="ux-process-step-description">We create detailed user personas and map their journeys to identify pain points and opportunities for improvement.</p>
                    </div>

                    <div class="ux-process-step scroll-reveal">
                        <div class="ux-process-step-number">03</div>
                        <h3 class="ux-process-step-title">Wireframing & Prototyping</h3>
                        <p class="ux-process-step-description">We develop low-fidelity wireframes and interactive prototypes to visualize the user experience and gather early feedback.</p>
                    </div>

                    <div class="ux-process-step scroll-reveal">
                        <div class="ux-process-step-number">04</div>
                        <h3 class="ux-process-step-title">UI Design & Visual Refinement</h3>
                        <p class="ux-process-step-description">We create high-fidelity UI designs that align with your brand identity and enhance the overall user experience.</p>
                    </div>

                    <div class="ux-process-step scroll-reveal">
                        <div class="ux-process-step-number">05</div>
                        <h3 class="ux-process-step-title">Usability Testing & Iteration</h3>
                        <p class="ux-process-step-description">We conduct usability testing with real users and iterate on the design based on feedback to ensure optimal performance.</p>
                    </div>

                    <div class="ux-process-step scroll-reveal">
                        <div class="ux-process-step-number">06</div>
                        <h3 class="ux-process-step-title">Implementation & Support</h3>
                        <p class="ux-process-step-description">We provide detailed design specifications and support throughout the implementation process to ensure design integrity.</p>
                    </div>
                </div>
            </section>
        </div>

        <section class="ux-case-studies">
            <div class="ux-container">
                <h2 class="ux-section-heading">Success Stories</h2>
                <p class="ux-section-description">Real-world examples of our UX/UI design impact</p>

                <div class="ux-case-studies-list">
                    <div class="ux-case-study-item scroll-reveal">
                        <div class="case-study-content-wrapper">
                            <div class="case-study-text">
                                <span class="case-study-step-number">01</span>
                                <h3>E-commerce Platform Redesign</h3>
                                <p>Redesigned a major e-commerce platform's user interface, resulting in 45% increase in conversion rates and 60% reduction in cart abandonment.</p>
                                <button class="case-study-button" data-case-study="case-study-1">View Details</button>
                            </div>
                        </div>
                    </div>

                    <div class="ux-case-study-item scroll-reveal">
                        <div class="case-study-content-wrapper">
                            <div class="case-study-text">
                                <span class="case-study-step-number">02</span>
                                <h3>Banking App Transformation</h3>
                                <p>Revamped a mobile banking application's UX/UI, achieving 95% user satisfaction and 30% increase in mobile transactions.</p>
                                <button class="case-study-button" data-case-study="case-study-2">View Details</button>
                            </div>
                        </div>
                    </div>

                    <div class="ux-case-study-item scroll-reveal">
                        <div class="case-study-content-wrapper">
                            <div class="case-study-text">
                                <span class="case-study-step-number">03</span>
                                <h3>Healthcare Portal Enhancement</h3>
                                <p>Improved a healthcare portal's user experience, resulting in 50% faster appointment booking and 40% reduction in support tickets.</p>
                                <button class="case-study-button" data-case-study="case-study-3">View Details</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="ux-technologies">
            <div class="ux-container">
                <h2 class="ux-section-heading">Design Technologies</h2>
                <p class="ux-section-description">We leverage industry-leading tools and technologies</p>

                <div class="ux-tech-grid">
                    <div class="ux-tech-item scroll-reveal">
                        <div class="ux-tech-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 19l7-7 3 3-7 7-3-3z"></path>
                                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path>
                                <path d="M2 2l7.586 7.586"></path>
                                <circle cx="11" cy="11" r="2"></circle>
                            </svg>
                        </div>
                        <p>Figma</p>
                    </div>
                    <div class="ux-tech-item scroll-reveal">
                        <div class="ux-tech-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 19l7-7 3 3-7 7-3-3z"></path>
                                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path>
                                <path d="M2 2l7.586 7.586"></path>
                                <circle cx="11" cy="11" r="2"></circle>
                            </svg>
                        </div>
                        <p>Adobe XD</p>
                    </div>
                    <div class="ux-tech-item scroll-reveal">
                        <div class="ux-tech-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 19l7-7 3 3-7 7-3-3z"></path>
                                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path>
                                <path d="M2 2l7.586 7.586"></path>
                                <circle cx="11" cy="11" r="2"></circle>
                            </svg>
                        </div>
                        <p>Sketch</p>
                    </div>
                    <div class="ux-tech-item scroll-reveal">
                        <div class="ux-tech-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 19l7-7 3 3-7 7-3-3z"></path>
                                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path>
                                <path d="M2 2l7.586 7.586"></path>
                                <circle cx="11" cy="11" r="2"></circle>
                            </svg>
                        </div>
                        <p>InVision</p>
                    </div>
                    <div class="ux-tech-item scroll-reveal">
                        <div class="ux-tech-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 19l7-7 3 3-7 7-3-3z"></path>
                                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path>
                                <path d="M2 2l7.586 7.586"></path>
                                <circle cx="11" cy="11" r="2"></circle>
                            </svg>
                        </div>
                        <p>Adobe Creative Suite</p>
                    </div>
                    <div class="ux-tech-item scroll-reveal">
                        <div class="ux-tech-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 19l7-7 3 3-7 7-3-3z"></path>
                                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path>
                                <path d="M2 2l7.586 7.586"></path>
                                <circle cx="11" cy="11" r="2"></circle>
                            </svg>
                        </div>
                        <p>Principle</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="ux-cta">
            <div class="ux-container">
                <h2 class="ux-section-heading">Ready to Transform Your Digital Experience?</h2>
                <p class="ux-section-description">Let's collaborate to create exceptional user experiences that drive results</p>
                <a href="#contact" class="ux-btn">Start Your Project</a>
            </div>
        </section>
    </main>

    <!-- Case Study Modal -->
    <div id="caseStudyModal" class="modal">
        <div class="modal-content">
            <button class="close-modal">&times;</button>
            <div class="case-study-details">
            </div>
        </div>
    </div>

    <div id="footer-container"></div>

    <script src="js/components/absolute-navbar.js"></script>
    <script src="js/components/footer.js"></script>
    <script src="js/ux-ui-design.js"></script>
    <script src="js/components/scroll-to-top.js"></script>
</body>
</html>