<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Base URL for all relative paths -->
    <base href="/">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <meta name="title" content="Software Development | Saya Setona">
    <meta name="description" content="Discover custom software development solutions from Saya Setona. Our expert team builds innovative, scalable, and secure applications to drive your business forward.">
    <meta name="keywords" content="software development, custom software solutions, web development, mobile app development, API development, innovative software">
    <meta name="author" content="Saya Setona">
    <meta name="robots" content="index, follow">

    <meta property="og:title" content="Software Development | Saya Setona">
    <meta property="og:description" content="Explore custom software development services by Saya Setona designed to drive business growth through innovative technology solutions.">
    <meta property="og:url" content="https://www.saya-setona.co.za/services/software-development">
    <meta property="og:type" content="website">
    <meta property="og:image" content="https://www.saya-setona.co.za/images/software-hero.png">

    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Software Development | Saya Setona">
    <meta name="twitter:description" content="Explore custom software development services by Saya Setona designed to drive business growth through innovative technology solutions.">
    <meta name="twitter:image" content="https://www.saya-setona.co.za/images/software-hero.png">

    <link rel="canonical" href="https://www.saya-setona.co.za/services/software-development">
    <title>Software Development | Saya Setona</title>
    <link rel="icon" type="image/png" href="images/logo.png">

    <link rel="stylesheet" href="css/navbar.css">
    <link rel="stylesheet" href="css/footer.css">
    <link rel="stylesheet" href="css/software-development.css">
    <link rel="stylesheet" href="css/scroll-to-top.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <div id="navbar-container"></div>

    <main class="software-content">
        <section class="software-hero">
            <div class="software-container">
                <div class="software-hero-wrapper">
                    <div class="software-hero-content">
                        <h1 class="gradient-text">Software Development</h1>
                        <p>Building innovative solutions for tomorrow's challenges. Our expert team creates custom software that drives business growth, enhances efficiency, and delivers exceptional user experiences.</p>
                        <a href="#services" class="software-btn software-hero-btn button-hover">Explore Services</a>
                    </div>
                    <div class="software-hero-image-container floating-element">
                        <img src="../../images/software-hero.png" alt="Software Development" class="software-hero-image dynamic-shadow" loading="lazy">
                    </div>
                </div>
            </div>
        </section>

        <section id="services" class="software-services">
            <div class="software-container">
                <h2 class="software-section-heading scroll-reveal">Our Development Services</h2>
                <p class="software-section-description scroll-reveal">We create tailored software solutions that drive business growth and efficiency. Our expert team combines technical excellence with industry best practices to deliver robust, scalable, and secure applications.</p>

                <div class="software-services-grid">
                    <div class="software-service-card scroll-reveal">
                        <h3 class="software-service-title">Custom Application Development</h3>
                        <p class="software-service-description">Tailored software solutions designed to meet your specific business needs and objectives, from concept to deployment and beyond.</p>
                    </div>
                    <div class="software-service-card scroll-reveal">
                        <h3 class="software-service-title">Web Development</h3>
                        <p class="software-service-description">Responsive and dynamic web applications built with modern technologies that deliver exceptional user experiences across all devices.</p>
                    </div>
                    <div class="software-service-card scroll-reveal">
                        <h3 class="software-service-title">Mobile App Development</h3>
                        <p class="software-service-description">Native and cross-platform mobile applications for iOS and Android that engage users and drive business growth.</p>
                    </div>
                    <div class="software-service-card scroll-reveal">
                        <h3 class="software-service-title">API Development</h3>
                        <p class="software-service-description">Secure and scalable APIs that connect your systems seamlessly, enabling data exchange and integration with third-party services.</p>
                    </div>
                    <div class="software-service-card scroll-reveal">
                        <h3 class="software-service-title">Cloud Solutions</h3>
                        <p class="software-service-description">Cloud-native applications and migration services that leverage the full potential of modern cloud platforms for scalability and reliability.</p>
                    </div>
                    <div class="software-service-card scroll-reveal">
                        <h3 class="software-service-title">DevOps & CI/CD</h3>
                        <p class="software-service-description">Implementation of DevOps practices and continuous integration/continuous deployment pipelines to accelerate your development lifecycle.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="software-stats">
            <div class="software-container">
                <h2 class="software-section-heading scroll-reveal">Our Track Record</h2>
                <p class="software-section-description scroll-reveal">We have a proven history of delivering successful software projects across various industries.</p>

                <div class="software-stats-grid">
                    <div class="software-stat-item scroll-reveal">
                        <div class="software-stat-number">200+</div>
                        <div class="software-stat-label">Successful Projects</div>
                    </div>
                    <div class="software-stat-item scroll-reveal">
                        <div class="software-stat-number">98%</div>
                        <div class="software-stat-label">Client Satisfaction</div>
                    </div>
                    <div class="software-stat-item scroll-reveal">
                        <div class="software-stat-number">15+</div>
                        <div class="software-stat-label">Years Experience</div>
                    </div>
                    <div class="software-stat-item scroll-reveal">
                        <div class="software-stat-number">40+</div>
                        <div class="software-stat-label">Tech Experts</div>
                    </div>
                </div>
            </div>
        </section>

        <section class="software-process">
            <div class="software-container">
                <h2 class="software-section-heading scroll-reveal">Our Development Process</h2>
                <p class="software-section-description scroll-reveal">We follow a structured approach to ensure the successful delivery of your software project.</p>

                <div class="process-steps">
                    <div class="process-step scroll-reveal">
                        <div class="process-step-number">1</div>
                        <h3 class="process-step-title">Discovery</h3>
                        <p class="process-step-description">We begin by understanding your business objectives, user needs, and technical requirements through in-depth consultations.</p>
                    </div>
                    <div class="process-step scroll-reveal">
                        <div class="process-step-number">2</div>
                        <h3 class="process-step-title">Planning</h3>
                        <p class="process-step-description">Our team creates a detailed project roadmap, including architecture design, technology stack selection, and sprint planning.</p>
                    </div>
                    <div class="process-step scroll-reveal">
                        <div class="process-step-number">3</div>
                        <h3 class="process-step-title">Development</h3>
                        <p class="process-step-description">We build your solution using agile methodologies, with regular demos and feedback sessions to ensure alignment with your vision.</p>
                    </div>
                    <div class="process-step scroll-reveal">
                        <div class="process-step-number">4</div>
                        <h3 class="process-step-title">Testing</h3>
                        <p class="process-step-description">Rigorous quality assurance testing ensures your software is robust, secure, and performs optimally under all conditions.</p>
                    </div>
                    <div class="process-step scroll-reveal">
                        <div class="process-step-number">5</div>
                        <h3 class="process-step-title">Deployment</h3>
                        <p class="process-step-description">We handle the smooth deployment of your solution, ensuring a seamless transition to production environments.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="software-technologies">
            <div class="software-container">
                <h2 class="software-section-heading scroll-reveal">Technologies We Use</h2>
                <p class="software-section-description scroll-reveal">We leverage cutting-edge technologies to build modern, scalable, and secure software solutions.</p>

                <div class="software-tech-grid">
                    <div class="software-tech-item scroll-reveal">
                        <svg class="software-tech-icon" width="64" height="64" viewBox="0 0 841.9 595.3" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="420.9" cy="296.5" r="45.7" fill="#7B68EE"/>
                            <path d="M520 0c-50 70-100 140-150 210C320 280 270 350 220 420 170 490 120 560 70 630 20 700-30 770-80 840" stroke="#7B68EE" stroke-width="20" fill="none"/>
                        </svg>
                        <p>React</p>
                    </div>
                    <div class="software-tech-item scroll-reveal">
                        <svg class="software-tech-icon" width="64" height="64" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
                            <path d="M128,0L0,64v128L128,256l128-64V64Z" fill="#7B68EE"/>
                            <text x="50%" y="55%" fill="#fff" font-size="50" text-anchor="middle" dominant-baseline="middle">N</text>
                        </svg>
                        <p>Node.js</p>
                    </div>
                    <div class="software-tech-item scroll-reveal">
                        <svg class="software-tech-icon" width="64" height="64" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
                            <rect width="256" height="256" rx="40" fill="#7B68EE"/>
                            <text x="50%" y="55%" fill="#fff" font-size="60" text-anchor="middle" dominant-baseline="middle">Py</text>
                        </svg>
                        <p>Python</p>
                    </div>
                    <div class="software-tech-item scroll-reveal">
                        <svg class="software-tech-icon" width="64" height="64" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                            <rect width="512" height="512" fill="#7B68EE"/>
                            <text x="50%" y="55%" fill="#fff" font-size="80" text-anchor="middle" dominant-baseline="middle">AWS</text>
                        </svg>
                        <p>AWS</p>
                    </div>
                    <div class="software-tech-item scroll-reveal">
                        <svg class="software-tech-icon" width="64" height="64" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
                            <rect width="256" height="256" rx="40" fill="#7B68EE"/>
                            <text x="50%" y="55%" fill="#fff" font-size="60" text-anchor="middle" dominant-baseline="middle">TS</text>
                        </svg>
                        <p>TypeScript</p>
                    </div>
                    <div class="software-tech-item scroll-reveal">
                        <svg class="software-tech-icon" width="64" height="64" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
                            <rect width="256" height="256" rx="40" fill="#7B68EE"/>
                            <text x="50%" y="55%" fill="#fff" font-size="60" text-anchor="middle" dominant-baseline="middle">K8s</text>
                        </svg>
                        <p>Kubernetes</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="software-projects">
            <div class="software-container">
                <h2 class="software-section-heading scroll-reveal">Featured Projects</h2>
                <p class="software-section-description scroll-reveal">Explore some of our recent software development projects that have delivered exceptional results for our clients.</p>

                <div class="software-projects-grid">
                    <div class="software-project-card scroll-reveal">
                        <div class="project-content">
                            <div class="project-text">
                                <span class="project-tag">Enterprise</span>
                                <h3>Financial Services Platform</h3>
                                <p>A comprehensive digital platform for a leading financial institution, featuring secure customer portals, real-time transaction processing, and advanced analytics dashboards.</p>
                                <div class="code-block">
                                    <pre><span class="code-comment">// Secure transaction processing</span>
const processTransaction = async (data) => {
  try {
    const result = await api.secureProcess(data);
    return { success: true, data: result };
  } catch (error) {
    logger.error(error);
    return { success: false, error };
  }
};</pre>
                                </div>
                                <a href="#" class="project-button button-hover">View Case Study</a>
                            </div>
                        </div>
                    </div>

                    <div class="software-project-card scroll-reveal">
                        <div class="project-content">
                            <div class="project-text">
                                <span class="project-tag">Mobile</span>
                                <h3>Healthcare Mobile App</h3>
                                <p>A cross-platform mobile application for a healthcare provider, enabling patients to schedule appointments, access medical records, and communicate with healthcare professionals securely.</p>
                                <div class="code-block">
                                    <pre><span class="code-comment">// React Native component</span>
const AppointmentScheduler = () => {
  const [date, setDate] = useState(new Date());
  const [available, setAvailable] = useState([]);

  useEffect(() => {
    fetchAvailableSlots(date);
  }, [date]);

  return (
    <SchedulerView date={date} slots={available} />
  );
};</pre>
                                </div>
                                <a href="#" class="project-button button-hover">View Case Study</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="js/components/absolute-navbar.js"></script>
    <script src="js/components/footer.js"></script>
    <script src="js/components/scroll-to-top.js"></script>

    <script>
        // Scroll Reveal Animation
        document.addEventListener('DOMContentLoaded', function() {
            const revealElements = document.querySelectorAll('.scroll-reveal');

            function checkReveal() {
                revealElements.forEach(element => {
                    const elementTop = element.getBoundingClientRect().top;
                    const windowHeight = window.innerHeight;

                    if (elementTop < windowHeight - 100) {
                        element.classList.add('revealed');
                    }
                });
            }

            // Initial check
            checkReveal();

            // Check on scroll
            window.addEventListener('scroll', checkReveal);
        });
    </script>
</body>
</html>