<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="title" content="Say<PERSON> Setona | Careers">
    <meta name="description" content="Join the Saya Setona team and build innovative technology solutions that transform industries. Explore our career opportunities and grow with us.">
    <meta name="keywords" content="careers, jobs, technology careers, software development jobs, IT careers, tech jobs, employment">
    <meta name="author" content="Saya Setona">
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="Saya Setona | Careers">
    <meta property="og:description" content="Join our team and build innovative technology solutions that transform industries. Explore our career opportunities.">
    <meta property="og:url" content="https://www.saya-setona.co.za/careers">
    <meta property="og:type" content="website">

    <title>Saya Setona | Careers</title>
    <link rel="icon" type="image/png" href="../images/logo.png">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/footer.css">
    <link rel="stylesheet" href="../css/scroll-to-top.css">
    <base href="/">
    <link rel="stylesheet" href="../css/navbar.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        /* Page-specific styles */
        .hero-careers {
            background: linear-gradient(rgba(9, 17, 46, 0.8), rgba(9, 17, 46, 0.9)), url('../images/careers-hero.jpg');
            background-size: cover;
            background-position: center;
            padding: 120px 0 80px;
            color: white;
        }
        
        .page-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 60px 40px;
        }
        
        .content-section {
            margin-bottom: 60px;
        }
        
        .content-section h2 {
            font-size: 32px;
            margin-bottom: 20px;
            color: #09112E;
        }
        
        .content-section p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #333;
        }
        
        .values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .value-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            text-align: center;
        }
        
        .value-icon {
            font-size: 36px;
            margin-bottom: 15px;
            color: #09112E;
        }
        
        .value-card h3 {
            font-size: 20px;
            margin-bottom: 15px;
            color: #09112E;
        }
        
        .value-card p {
            font-size: 15px;
            line-height: 1.5;
            color: #555;
        }
        
        .job-listings {
            margin-top: 40px;
        }
        
        .job-card {
            background: white;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .job-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .job-title {
            font-size: 22px;
            color: #09112E;
            margin: 0;
        }
        
        .job-location {
            font-size: 14px;
            color: #555;
            background: #f0f4f8;
            padding: 5px 12px;
            border-radius: 20px;
        }
        
        .job-description {
            margin-bottom: 20px;
        }
        
        .job-meta {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #555;
        }
        
        .apply-button {
            display: inline-block;
            background-color: #09112E;
            color: white;
            padding: 10px 25px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }
        
        .apply-button:hover {
            background-color: #1a2a5e;
        }
        
        .benefits-section {
            background-color: #f0f4f8;
            padding: 60px 0;
        }
        
        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .benefit-card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }
        
        .benefit-card h3 {
            font-size: 18px;
            margin-bottom: 10px;
            color: #09112E;
        }
        
        .benefit-card p {
            font-size: 15px;
            line-height: 1.5;
            color: #555;
        }
        
        @media (max-width: 768px) {
            .values-grid, .benefits-grid {
                grid-template-columns: 1fr;
            }
            
            .job-header {
                flex-direction: column;
            }
            
            .job-location {
                margin-top: 10px;
            }
        }
    </style>
</head>

<body>
    <!-- Background shape element -->
    <div class="background-shape"></div>
    
    <!-- Navbar will be loaded here -->
    <div id="navbar-container"></div>
    
    <main>
        <section class="hero-careers">
            <div class="container">
                <div class="hero-content">
                    <h1>Join Our Team</h1>
                    <p>Build innovative solutions that transform industries and make a difference</p>
                </div>
            </div>
        </section>

        <div class="page-content">
            <section class="content-section">
                <h2>Why Work With Us</h2>
                <p>At Saya Setona, we're more than just a technology company. We're a team of passionate innovators committed to creating solutions that drive meaningful change across industries. When you join our team, you become part of a diverse, collaborative community that values creativity, excellence, and continuous growth.</p>
                <p>We believe that our people are our greatest asset, and we're dedicated to creating an environment where you can thrive professionally and personally. Whether you're an experienced professional or just starting your career, we offer opportunities to work on challenging projects, learn from industry experts, and make a real impact.</p>
                
                <div class="values-grid">
                    <div class="value-card">
                        <div class="value-icon">🌟</div>
                        <h3>Innovation</h3>
                        <p>We encourage creative thinking and bold ideas that challenge the status quo and drive technological advancement.</p>
                    </div>
                    <div class="value-card">
                        <div class="value-icon">🤝</div>
                        <h3>Collaboration</h3>
                        <p>We believe in the power of diverse perspectives and work together across teams to achieve exceptional results.</p>
                    </div>
                    <div class="value-card">
                        <div class="value-icon">🚀</div>
                        <h3>Excellence</h3>
                        <p>We strive for the highest standards in everything we do, from code quality to client relationships.</p>
                    </div>
                    <div class="value-card">
                        <div class="value-icon">🌍</div>
                        <h3>Impact</h3>
                        <p>We're committed to creating technology that makes a positive difference in businesses and communities.</p>
                    </div>
                </div>
            </section>
            
            <section class="content-section">
                <h2>Open Positions</h2>
                <p>We're always looking for talented individuals to join our growing team. Explore our current openings below and find the role that matches your skills and aspirations.</p>
                
                <div class="job-listings">
                    <div class="job-card">
                        <div class="job-header">
                            <h3 class="job-title">Senior Software Engineer</h3>
                            <span class="job-location">Johannesburg, South Africa</span>
                        </div>
                        <div class="job-description">
                            <p>We're looking for an experienced Software Engineer to join our development team, working on innovative solutions for our enterprise clients across various industries.</p>
                        </div>
                        <div class="job-meta">
                            <span>Full-time</span>
                            <span>5+ years experience</span>
                        </div>
                        <a href="#" class="apply-button">Apply Now</a>
                    </div>
                    
                    <div class="job-card">
                        <div class="job-header">
                            <h3 class="job-title">UX/UI Designer</h3>
                            <span class="job-location">Cape Town, South Africa</span>
                        </div>
                        <div class="job-description">
                            <p>Join our design team to create intuitive, engaging user experiences for our digital products and solutions. You'll collaborate with developers and product managers to bring designs to life.</p>
                        </div>
                        <div class="job-meta">
                            <span>Full-time</span>
                            <span>3+ years experience</span>
                        </div>
                        <a href="#" class="apply-button">Apply Now</a>
                    </div>
                    
                    <div class="job-card">
                        <div class="job-header">
                            <h3 class="job-title">Data Scientist</h3>
                            <span class="job-location">Remote, South Africa</span>
                        </div>
                        <div class="job-description">
                            <p>We're seeking a talented Data Scientist to help our clients extract valuable insights from their data and implement AI/ML solutions that drive business value.</p>
                        </div>
                        <div class="job-meta">
                            <span>Full-time</span>
                            <span>2+ years experience</span>
                        </div>
                        <a href="#" class="apply-button">Apply Now</a>
                    </div>
                    
                    <div class="job-card">
                        <div class="job-header">
                            <h3 class="job-title">Project Manager</h3>
                            <span class="job-location">Pretoria, South Africa</span>
                        </div>
                        <div class="job-description">
                            <p>Lead cross-functional teams to deliver complex technology projects on time and within budget. You'll work closely with clients and internal teams to ensure project success.</p>
                        </div>
                        <div class="job-meta">
                            <span>Full-time</span>
                            <span>4+ years experience</span>
                        </div>
                        <a href="#" class="apply-button">Apply Now</a>
                    </div>
                </div>
            </section>
        </div>
        
        <section class="benefits-section">
            <div class="page-content">
                <h2>Benefits & Perks</h2>
                <p>We believe in taking care of our team members and providing an environment where you can thrive both professionally and personally.</p>
                
                <div class="benefits-grid">
                    <div class="benefit-card">
                        <h3>Competitive Compensation</h3>
                        <p>We offer competitive salaries and performance-based bonuses to recognize your contributions.</p>
                    </div>
                    <div class="benefit-card">
                        <h3>Health & Wellness</h3>
                        <p>Comprehensive medical aid, wellness programs, and mental health support for you and your family.</p>
                    </div>
                    <div class="benefit-card">
                        <h3>Professional Development</h3>
                        <p>Continuous learning opportunities, including training budgets, conferences, and certification support.</p>
                    </div>
                    <div class="benefit-card">
                        <h3>Flexible Work</h3>
                        <p>Hybrid work options and flexible schedules to help you maintain work-life balance.</p>
                    </div>
                    <div class="benefit-card">
                        <h3>Paid Time Off</h3>
                        <p>Generous leave policy including annual leave, sick leave, and family responsibility leave.</p>
                    </div>
                    <div class="benefit-card">
                        <h3>Team Building</h3>
                        <p>Regular team events, social activities, and opportunities to connect with colleagues.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Scripts -->
   <script src="js/components/absolute-navbar.js"></script>
   <script src="../js/components/footer.js"></script>
   <script src="../js/components/scroll-to-top.js"></script>
</html>
