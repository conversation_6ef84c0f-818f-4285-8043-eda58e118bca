<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Story - Saya-Setona</title>
    <link rel="icon" type="image/png" href="../images/logo.png">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/navbar.css">
    <link rel="stylesheet" href="../css/ourstory.css">
    <link rel="stylesheet" href="../css/footer.css">
    <link rel="stylesheet" href="../css/scroll-to-top.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div id="navbar-container"></div>

    <div class="main-container">
        <!-- Our Story Section -->
        <section class="section section-reverse">
            <div class="section-content">
                <h2 class="section-title">Our Story</h2>
                <h2 class="section-title">Where we started</h2>
                <p class="section-text">Saya-Setona was founded in 2013 out of the Innovation Hub in Pretoria, with the vision for a technology services company which could identify and draw in outstanding talent in software engineering.</p>
            </div>
            <img src="../images/image1.png" alt="Where we started" class="section-image">
        </section>

        <!-- Our Vision Section -->
        <section class="section">
            <div class="section-content">
                <h2 class="section-title">Our Vision</h2>
                <p class="section-text">At the heart of every great innovation is a vision—a desire to create meaningful change. For us, that vision was born out of a growing concern: the disconnect between citizens and government, the lack of accountability, and the inefficiencies in service delivery. South Africa, like many other nations, faces challenges where citizens struggle to be heard, and governments grapple with bureaucracy and outdated systems. We saw an opportunity to bridge this gap, and so our journey began.</p>
            </div>
            <img src="../images/image2.png" alt="Our Vision" class="section-image">
        </section>

        <!-- The Beginning Section -->
        <section class="section section-reverse">
            <div class="section-content">
                <h2 class="section-title">The Beginning</h2>
                <p class="section-text">Recognizing the increasing frustration among citizens due to poor service delivery and government inaction, the United Nations Development Programme (UNDP) embarked on a mission to find a solution. We believed that by improving communication between government and its people, we could reduce service delivery protests, foster trust, and create a more transparent and accountable system.<br><br>
                The solution? A powerful, accessible, and real-time communication tool—Let's Talk. This mobile app and web platform was developed by a visionary young entrepreneur from Soweto, Moepi Setona. Inspired by research conducted with a Free State municipality, Moepi designed Let's Talk to function as a two-way communication platform, allowing citizens to report service delivery issues, receive government updates, engage with their communities, and even rate public services.</p>
            </div>
            <img src="../images/image3.png" alt="The Beginning" class="section-image">
        </section>

        <!-- The Innovation Section -->
        <div class="innovation-section">
            <div class="innovation-container">
                <h2 class="innovation-title">The Innovation</h2>
                <p class="innovation-text">Let's Talk enables citizens to interact with local and provincial governments as easily as sending a message to a friend. Whether through the app or SMS (for those without data access), citizens can:</p>
                <p class="innovation-text">Report service delivery issues and receive responses.<br>
                Get live updates from government on infrastructure projects, service disruptions, and policy changes.<br>
                Engage with community members and local representatives.<br>
                Rate government services, providing real-time feedback.</p>
                <p class="innovation-text">For government officials, the platform serves as a tool to:</p>
                <p class="innovation-text">Receive verified real-time reports from citizens.<br>
                Analyze service delivery trends and improve response strategies.<br>
                Communicate directly with the public in a transparent and efficient manner.</p>
                <p class="innovation-text">This initiative eliminates slow, paper-based reporting systems and costly telephone-based complaints, making it easier for citizens to engage with their local leaders.</p>
            </div>
        </div>

        <!-- The Journey Section -->
        <section class="section">
            <div class="section-content">
                <h2 class="section-title">The Journey</h2>
                <p class="section-text">Bringing this vision to life was not without challenges. Over eight months, our team traveled nearly 8,000 kilometers across South Africa, presenting our idea, forging partnerships, and overcoming bureaucratic hurdles. The year was marked by national and provincial elections, making government collaboration difficult as officials were heavily focused on election-related responsibilities.<br><br>
                Yet, persistence paid off. The Limpopo Province became our pilot partner, embracing Let's Talk as a means to enhance service delivery and public trust. On July 23, the platform was officially launched in Thulamela Local Municipality under the banner Let's Talk – Batho Pele. The phrase Batho Pele (meaning People First) embodies our commitment to prioritizing the needs of citizens.<br><br>
                The launch was met with enthusiasm. The mayor himself downloaded the app, and within hours, the first alerts, chat groups, and user registrations began rolling in. With this momentum, ward committees underwent training, ensuring that local representatives could maximize the potential of the platform.</p>
            </div>
            <img src="../images/image4.png" alt="The Journey" class="section-image">
        </section>

        <!-- Lessons and Growth Section -->
        <section class="section section-reverse">
            <div class="section-content">
                <h2 class="section-title">Lessons and Growth</h2>
                <p class="section-text">This journey has revealed the complexities of government partnerships, the need for persistent engagement, and the importance of localized solutions. We learned that decision-making within municipalities is layered and often slow, requiring strategic navigation of political and administrative structures.<br><br>
                We also recognized the need for inclusivity. As Let's Talk expanded, it became evident that language barriers could limit participation. While the app supports all languages in chat functions, the navigation system remains in English. Future iterations will prioritize multilingual accessibility to ensure that all citizens, regardless of background, can benefit from the platform.</p>
            </div>
            <img src="../images/image5.png" alt="Lessons and Growth" class="section-image">
        </section>

        <!-- The Future Section -->
        <section class="section">
            <div class="section-content">
                <h2 class="section-title">The Future</h2>
                <p class="section-text">With a successful pilot underway in Waterberg and the OR Tambo Districts, our focus now shifts to scaling this initiative. We aim to expand into more municipalities, strengthen partnerships, and ensure that Let's Talk continues to empower citizens and government alike. This project has already demonstrated its potential to revolutionize service delivery, and with continued support, we envision a South Africa where government and citizens work together seamlessly for a better future.<br><br>
                This is just the beginning.<br>
                The journey continues…</p>
            </div>
            <img src="../images/image6.png" alt="The Future" class="section-image">
        </section>
    </div>

    <!-- Footer in js/components folder (footer.js) -->
    <div id="footer-container"></div>

    <!-- Scripts -->
    <script src="../../js/components/navbar.js"></script>
    <script src="../js/components/footer.js"></script>
    <script src="../js/components/scroll-to-top.js"></script>
</body>
</html>
