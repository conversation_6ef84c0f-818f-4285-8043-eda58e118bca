// UX/UI Design Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Fix the hero image reference
    const heroImage = document.querySelector('.ux-hero-image');
    if (heroImage) {
        heroImage.src = '../../images/ux-ui-hero-bg.jpeg';
        heroImage.alt = 'UX/UI Design Services';
    }

    // Add scroll reveal animation
    const scrollRevealElements = document.querySelectorAll('.scroll-reveal');
    
    function checkScroll() {
        scrollRevealElements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            if (elementTop < windowHeight - 100) {
                element.classList.add('revealed');
            }
        });
    }
    
    // Check on initial load
    checkScroll();
    
    // Check on scroll
    window.addEventListener('scroll', checkScroll);
    
    // Case study modal functionality
    const modal = document.getElementById('caseStudyModal');
    const closeModal = document.querySelector('.close-modal');
    const caseStudyButtons = document.querySelectorAll('.case-study-button');
    
    // Case study data
    const caseStudies = {
        'case-study-1': {
            title: 'E-commerce Platform Redesign',
            description: 'We completely redesigned the user interface and experience of a major e-commerce platform, focusing on streamlining the shopping journey and reducing friction points in the checkout process.',
            results: [
                '45% increase in conversion rates',
                '60% reduction in cart abandonment',
                '30% increase in average order value',
                '25% increase in customer retention'
            ],
            technologies: ['Figma', 'Adobe XD', 'InVision', 'Hotjar', 'Google Analytics']
        },
        'case-study-2': {
            title: 'Banking App Transformation',
            description: 'Our team revamped a mobile banking application with a focus on simplifying complex financial transactions and improving accessibility for users of all technical abilities.',
            results: [
                '95% user satisfaction rating',
                '30% increase in mobile transactions',
                '40% reduction in support tickets',
                '50% faster task completion time'
            ],
            technologies: ['Sketch', 'Principle', 'Adobe Creative Suite', 'UserTesting', 'Maze']
        },
        'case-study-3': {
            title: 'Healthcare Portal Enhancement',
            description: 'We improved a healthcare portal\'s user experience to make appointment booking and medical record access more intuitive for patients and healthcare providers.',
            results: [
                '50% faster appointment booking process',
                '40% reduction in support tickets',
                '35% increase in patient portal adoption',
                '25% improvement in patient satisfaction scores'
            ],
            technologies: ['Figma', 'Adobe XD', 'InVision', 'Optimal Workshop', 'Maze']
        }
    };
    
    // Open modal with case study details
    caseStudyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const caseStudyId = this.getAttribute('data-case-study');
            const caseStudy = caseStudies[caseStudyId];
            
            if (caseStudy) {
                const modalContent = document.querySelector('.case-study-details');
                
                // Create HTML for case study details
                let html = `
                    <h2>${caseStudy.title}</h2>
                    <p class="case-study-description">${caseStudy.description}</p>
                    
                    <div class="case-study-results">
                        <h3>Key Results</h3>
                        <ul>
                            ${caseStudy.results.map(result => `<li>${result}</li>`).join('')}
                        </ul>
                    </div>
                    
                    <div class="case-study-technologies">
                        <h3>Technologies Used</h3>
                        <div class="tech-tags">
                            ${caseStudy.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                        </div>
                    </div>
                `;
                
                modalContent.innerHTML = html;
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        });
    });
    
    // Close modal
    closeModal.addEventListener('click', function() {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    });
}); 