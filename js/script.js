// Form submission handling
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const email = document.getElementById('email').value;
            const phoneNumber = document.getElementById('phoneNumber').value;
            const message = document.getElementById('message').value;
            
            // Form validation
            if (!firstName || !lastName || !email || !phoneNumber || !message) {
                alert('Please fill in all fields.');
                return;
            }
            
            // Email validation
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailPattern.test(email)) {
                alert('Please enter a valid email address.');
                return;
            }
            
            // would typically send the form data to your server here
            // 
            alert('Message sent successfully! We will get back to you soon.');
            contactForm.reset();
        });
    }
    // Dropdown menu functionality
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        const link = dropdown.querySelector('a');
        const icon = dropdown.querySelector('.dropdown-icon');
        
        // Toggle the dropdown menu visibility when clicking the link
        if (link) {
            link.addEventListener('click', function() {
                const menu = dropdown.querySelector('.dropdown-menu');
                if (menu) {
                    menu.classList.toggle('show'); // Add or remove the 'show' class to control visibility
                    
                    // Optional: Toggle the icon to indicate open/close
                    if (icon) {
                        icon.classList.toggle('rotate'); // Assuming you have a class for rotating the icon when open
                    }
                }
            });
        }
    });
});