const renderFooter = function() {
    // Determine the correct path prefix based on the current page location
    let pathPrefix = "";
    const currentPath = window.location.pathname;

    if (currentPath.includes("/pages/Industries/") || currentPath.includes("/pages/Projects/")) {
        pathPrefix = "../../"; // Two levels up
    } else if (currentPath.includes("/pages/")) {
        pathPrefix = "../"; // One level up
    } else {
        pathPrefix = ""; // Already at root level
    }

    const footerHTML = `
        <footer>
            <div class="footer-content">
                <div class="footer-section wide-section">
                    <h3>Industries</h3>
                    <div class="two-columns">
                        <div class="column">
                            <ul>
                                <li><a href="${pathPrefix}pages/Industries/agriculture">Agriculture</a></li>
                                <li><a href="${pathPrefix}pages/Industries/banking">Banking</a></li>
                                <li><a href="${pathPrefix}pages/Industries/education">Education</a></li>
                                <li><a href="${pathPrefix}pages/Industries/energy">Energy</a></li>
                                <li><a href="${pathPrefix}pages/Industries/financial-services">Financial Services</a></li>
                            </ul>
                        </div>
                        <div class="column">
                            <ul>
                                <li><a href="${pathPrefix}pages/Industries/healthcare">Healthcare</a></li>
                                <li><a href="${pathPrefix}pages/Industries/manufacturing">Manufacturing</a></li>
                                <li><a href="${pathPrefix}pages/Industries/media">Media</a></li>
                                <li><a href="${pathPrefix}pages/Industries/mining">Mining</a></li>
                                <li><a href="${pathPrefix}pages/Industries/public-sector">Public Sector</a></li>
                                <li><a href="${pathPrefix}pages/Industries/retail">Retail</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>Services</h3>
                    <ul>
                        <li><a href="${pathPrefix}pages/services/cloud-services">Cloud Services</a></li>
                        <li><a href="${pathPrefix}pages/services/consulting">Consulting</a></li>
                        <li><a href="${pathPrefix}pages/services/cybersecurity">Cybersecurity</a></li>
                        <li><a href="${pathPrefix}pages/services/data-analytics">Data Analytics</a></li>
                        <li><a href="${pathPrefix}pages/services/digital-transformation">Digital Transformation</a></li>
                        <li><a href="${pathPrefix}pages/services/software-development">Software Development</a></li>
                        <li><a href="${pathPrefix}pages/services/systems-integration">Systems Integration</a></li>
                        <li><a href="${pathPrefix}pages/services/ux-ui-design">UX/UI Design</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>More</h3>
                    <ul>
                        <li><a href="${pathPrefix}pages/insights/articles">Articles</a></li>
                        <li><a href="${pathPrefix}pages/insights/success-stories">Case Studies</a></li>
                        <li><a href="${pathPrefix}pages/ourstory">About Us</a></li>
                        <li><a href="${pathPrefix}pages/giving-back">Giving Back</a></li>
                        <li><a href="https://za.linkedin.com/company/saya-setona-pty-ltd">Careers</a></li>
                        <li><a href="${pathPrefix}pages/contact">Contact</a></li>
                    </ul>
                </div>
            </div>
        </footer>
    `;

    // Append the footer to the correct container
    const footerContainer = document.getElementById('footer-container');
    if (footerContainer) {
        footerContainer.innerHTML = footerHTML;
    } else {
        document.body.insertAdjacentHTML('beforeend', footerHTML);
    }
};

// Execute the function when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', renderFooter);
