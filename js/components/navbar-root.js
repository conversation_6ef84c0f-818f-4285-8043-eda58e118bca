document.addEventListener('DOMContentLoaded', function () {
    // Build the navbar HTML with relative path references for the root directory
    const navbar = `
        <header class="navbar-header">
            <div class="navbar-container">
                <div class="navbar-logo-container">
                    <a href="/">
                        <img src="images/logo.png" alt="Saya-Setona Logo" class="navbar-logo">
                    </a>
                </div>

                <!-- Hamburger Menu Button for Mobile -->
                <div class="hamburger-menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>

                <nav class="navbar-nav">
                    <ul class="navbar-links navbar-center">
                        <li class="navbar-dropdown">
                            <a href="#">Industries</a>
                        </li>
                        <li class="navbar-dropdown">
                            <a href="#">Professional Services</a>
                        </li>
                        <li class="navbar-dropdown">
                            <a href="#">Insights</a>
                        </li>
                        <li><a href="pages/about">Let's Talk</a></li>
                    </ul>
                </nav>

                <div class="navbar-right">
                    <ul class="navbar-links">
                        <li class="navbar-dropdown">
                            <a href="pages/ourstory">About Us</a>
                        </li>
                        <li><a href="https://za.linkedin.com/company/saya-setona-pty-ltd">Careers</a></li>
                        <li><a href="pages/giving-back">Giving Back</a></li>
                        <li><a href="pages/contact">Contact</a></li>
                        <li>
                            <img src="images/southafricanflaground1.png" alt="Profile" class="navbar-profile-icon">
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Dropdown Menus (Full Width) -->
            <div class="navbar-dropdown-container" id="industries-dropdown">
                <div class="dropdown-content-wrapper">
                    <div class="dropdown-columns">
                        <div class="dropdown-column">
                            <ul>
                                <li><a href="pages/Industries/agriculture">Agriculture</a></li>
                                <li><a href="pages/Industries/banking">Banking</a></li>
                                <li><a href="pages/Industries/education">Education</a></li>
                                <li><a href="pages/Industries/energy">Energy</a></li>
                                <li><a href="pages/Industries/financial-services">Financial Services</a></li>
                            </ul>
                        </div>
                        <div class="dropdown-column">
                            <ul>
                                <li><a href="pages/Industries/healthcare">Healthcare</a></li>
                                <li><a href="pages/Industries/manufacturing">Manufacturing</a></li>
                                <li><a href="pages/Industries/media">Media</a></li>
                                <li><a href="pages/Industries/mining">Mining</a></li>
                                <li><a href="pages/Industries/public-sector">Public Sector</a></li>
                                <li><a href="pages/Industries/retail">Retail</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="navbar-dropdown-container" id="services-dropdown">
                <div class="dropdown-content-wrapper">
                    <div class="dropdown-columns">
                        <div class="dropdown-column">
                            <ul>
                                <li><a href="pages/services/cloud-services">Cloud Services</a></li>
                                <li><a href="pages/services/consulting">Consulting</a></li>
                                <li><a href="pages/services/cybersecurity">Cybersecurity</a></li>
                                <li><a href="pages/services/data-analytics">Data Analytics</a></li>
                            </ul>
                        </div>
                        <div class="dropdown-column">
                            <ul>
                                <li><a href="pages/services/digital-transformation">Digital Transformation</a></li>
                                <li><a href="pages/services/software-development">Software Development</a></li>
                                <li><a href="pages/services/systems-integration">Systems Integration</a></li>
                                <li><a href="pages/services/ux-ui-design">UX/UI Design</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="navbar-dropdown-container" id="insights-dropdown">
                <div class="dropdown-content-wrapper">
                    <div class="dropdown-columns">
                        <div class="dropdown-column">
                            <ul>
                                <li><a href="pages/insights/articles.html">Articles</a></li>
                                <li><a href="pages/insights/success-stories.html">Success Stories</a></li>
                                <li><a href="pages/insights/events.html">Events</a></li>
                                <li><a href="pages/insights/webinars.html">Webinars</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="navbar-dropdown-container" id="about-dropdown">
                <div class="dropdown-content-wrapper">
                    <div class="dropdown-columns">
                        <div class="dropdown-column">
                            <ul>
                                <li><a href="pages/ourstory">Our Story</a></li>
                                <li><a href="https://partners.amazonaws.com/partners/0018a00001qmNQmAAM/">Amazon Web Services</a></li>
                                <li><a href="https://www.ibm.com/us-en">IBM</a></li>
                                <li><a href="https://www.undp.org/south-africa/blog/long-journey-innovation">United Nations</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </header>
    `;

    // Insert the navbar into the container
    const navbarContainer = document.getElementById("navbar-container");
    if (navbarContainer) {
        navbarContainer.innerHTML = navbar;
    }

    // Dropdown Handling for Desktop
    document.querySelectorAll('.navbar-dropdown').forEach((item, index) => {
        const dropdown = document.querySelectorAll('.navbar-dropdown-container')[index];
        if (dropdown) {
            if (window.innerWidth > 768) {
                // Desktop behavior: hover
                item.addEventListener('mouseenter', function () {
                    closeAllDropdowns();
                    this.classList.add('active');
                    dropdown.classList.add('active');
                });

                item.addEventListener('mouseleave', function () {
                    setTimeout(() => {
                        if (!dropdown.matches(':hover')) {
                            dropdown.classList.remove('active');
                            item.classList.remove('active');
                        }
                    }, 100);
                });

                dropdown.addEventListener('mouseleave', function () {
                    this.classList.remove('active');
                    item.classList.remove('active');
                });
            }
        }
    });

    function closeAllDropdowns() {
        document.querySelectorAll('.navbar-dropdown-container.active, .navbar-dropdown.active').forEach(el => {
            el.classList.remove('active');
        });
    }

    // Mobile Menu Toggle with improved handling
    const hamburgerMenu = document.querySelector('.hamburger-menu');
    const navbarNav = document.querySelector('.navbar-nav');
    const navbarRight = document.querySelector('.navbar-right');
    let isMenuOpen = false;

    if (hamburgerMenu) {
        hamburgerMenu.addEventListener('click', function (e) {
            e.stopPropagation();
            isMenuOpen = !isMenuOpen;
            this.classList.toggle('active');
            navbarNav.classList.toggle('active');
            navbarRight.classList.toggle('active');

            if (!isMenuOpen) {
                closeAllDropdowns();
            }
        });
    }

    // Improved Mobile Dropdown Handling
    if (window.innerWidth <= 768) {
        // Create inline mobile dropdowns
        document.querySelectorAll('.navbar-dropdown').forEach((item, index) => {
            const dropdownContainer = document.querySelectorAll('.navbar-dropdown-container')[index];
            const link = item.querySelector('a');

            if (dropdownContainer && link) {
                // Create a mobile dropdown content container
                const mobileDropdownContent = document.createElement('div');
                mobileDropdownContent.className = 'mobile-dropdown-content';

                // Clone the dropdown links
                const dropdownLinks = dropdownContainer.querySelectorAll('a');
                dropdownLinks.forEach(dropdownLink => {
                    const linkClone = dropdownLink.cloneNode(true);
                    const listItem = document.createElement('li');
                    listItem.className = 'mobile-dropdown-item';
                    listItem.appendChild(linkClone);
                    mobileDropdownContent.appendChild(listItem);
                });

                // Insert the mobile dropdown content after the dropdown item
                item.appendChild(mobileDropdownContent);

                // Add touch-friendly click handling
                link.addEventListener('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const wasActive = item.classList.contains('active');

                    // Close other dropdowns
                    document.querySelectorAll('.navbar-dropdown').forEach(otherItem => {
                        if (otherItem !== item) {
                            otherItem.classList.remove('active');
                        }
                    });

                    // Toggle current dropdown
                    item.classList.toggle('active');

                    // If this dropdown was not active before, prevent immediate closing
                    if (!wasActive) {
                        e.stopPropagation();
                    }
                });
            }
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', function (e) {
            if (!e.target.closest('.navbar-dropdown') &&
                !e.target.closest('.hamburger-menu')) {
                document.querySelectorAll('.navbar-dropdown').forEach(item => {
                    item.classList.remove('active');
                });

                if (isMenuOpen) {
                    isMenuOpen = false;
                    hamburgerMenu?.classList.remove('active');
                    navbarNav?.classList.remove('active');
                    navbarRight?.classList.remove('active');
                }
            }
        });
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            // Reset mobile menu state when switching to desktop
            isMenuOpen = false;
            hamburgerMenu?.classList.remove('active');
            navbarNav?.classList.remove('active');
            navbarRight?.classList.remove('active');
            closeAllDropdowns();

            // Remove mobile dropdown content when switching to desktop
            document.querySelectorAll('.mobile-dropdown-content').forEach(content => {
                content.style.display = 'none';
            });
        } else {
            // We're switching to mobile view - make sure we have the mobile dropdowns
            if (!document.querySelector('.mobile-dropdown-content')) {
                // Reload the page to reinitialize mobile dropdowns
                window.location.reload();
            }
        }
    });
});
