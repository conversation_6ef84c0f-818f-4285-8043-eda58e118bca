// --- Helper Function: Throttle ---
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// --- Scroll to Top & Progress Indicator Logic ---

// Create container div
const scrollToTopContainer = document.createElement('div');
scrollToTopContainer.id = 'scroll-to-top-container';

// Create the button element
const scrollToTopButton = document.createElement('button');
scrollToTopButton.id = 'scroll-to-top-btn';
scrollToTopButton.setAttribute('aria-label', 'Scroll to top');
scrollToTopButton.setAttribute('title', 'Scroll to top');

// Simple SVG Arrow Icon
const arrowSVG = `<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="currentColor"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6 1.41 1.41z"/></svg>`;
scrollToTopButton.innerHTML = arrowSVG;

// Create the SVG progress ring element
const progressRingSVG = document.createElementNS("http://www.w3.org/2000/svg", "svg");
progressRingSVG.setAttribute("id", "progress-ring-svg");
progressRingSVG.setAttribute("viewBox", "0 0 36 36"); 

const progressRingBackground = document.createElementNS("http://www.w3.org/2000/svg", "circle");
progressRingBackground.setAttribute("id", "progress-ring-bg");
progressRingBackground.setAttribute("cx", "18");
progressRingBackground.setAttribute("cy", "18");
progressRingBackground.setAttribute("r", "15.9155"); // Radius calculated for circumference of 100

const progressRingIndicator = document.createElementNS("http://www.w3.org/2000/svg", "circle");
progressRingIndicator.setAttribute("id", "progress-ring-indicator");
progressRingIndicator.setAttribute("cx", "18");
progressRingIndicator.setAttribute("cy", "18");
progressRingIndicator.setAttribute("r", "15.9155"); // Radius calculated for circumference of 100

// Append circles to SVG
progressRingSVG.appendChild(progressRingBackground);
progressRingSVG.appendChild(progressRingIndicator);

// Append button and SVG to container
scrollToTopContainer.appendChild(progressRingSVG);
scrollToTopContainer.appendChild(scrollToTopButton);

// Append the container to the body
document.body.appendChild(scrollToTopContainer);

// Get the indicator circle and its circumference
const indicatorCircle = document.getElementById('progress-ring-indicator');
const radius = indicatorCircle.r.baseVal.value;
const circumference = 2 * Math.PI * radius;

// Set initial stroke properties
indicatorCircle.style.strokeDasharray = `${circumference} ${circumference}`;
indicatorCircle.style.strokeDashoffset = circumference;

// Function to update progress
function updateScrollProgress() {
    const scrollHeight = document.documentElement.scrollHeight;
    const clientHeight = document.documentElement.clientHeight;
    const scrollTop = window.scrollY || document.documentElement.scrollTop;

    // Avoid division by zero if scrollHeight == clientHeight
    const maxScroll = scrollHeight - clientHeight;
    const scrollFraction = maxScroll > 0 ? scrollTop / maxScroll : 0;

    // Ensure fraction is between 0 and 1
    const clampedFraction = Math.min(1, Math.max(0, scrollFraction));

    const offset = circumference - clampedFraction * circumference;
    indicatorCircle.style.strokeDashoffset = offset;

    // Toggle visibility based on scroll position (using the container now)
    if (scrollTop > 300) {
        scrollToTopContainer.classList.add('visible');
    } else {
        scrollToTopContainer.classList.remove('visible');
    }
}

// Function to scroll to top (remains the same)
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth' 
    });
}

// event listeners
// Throttle the scroll update function
window.addEventListener('scroll', throttle(updateScrollProgress, 50)); 
window.addEventListener('resize', throttle(updateScrollProgress, 100)); 
scrollToTopButton.addEventListener('click', scrollToTop);

updateScrollProgress();