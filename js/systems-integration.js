// Systems Integration Page JavaScript

// DOM Elements
const modal = document.getElementById('caseStudyModal');
const closeModalBtn = document.querySelector('.close-modal');
const caseStudyButtons = document.querySelectorAll('.case-study-button');
const scrollRevealElements = document.querySelectorAll('.scroll-reveal');

// Modal Functions
function openModal(caseStudyId) {
    const caseStudy = getCaseStudyData(caseStudyId);
    if (!caseStudy) return;

    const modalContent = document.querySelector('.modal-content');
    modalContent.innerHTML = `
        <button class="close-modal">&times;</button>
        <h2>${caseStudy.title}</h2>
        <div class="case-study-details">
            <p class="case-study-description">${caseStudy.description}</p>
            <div class="case-study-results">
                <h3>Results</h3>
                <ul>
                    ${caseStudy.results.map(result => `<li>${result}</li>`).join('')}
                </ul>
            </div>
            <div class="case-study-technologies">
                <h3>Technologies Used</h3>
                <div class="tech-tags">
                    ${caseStudy.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                </div>
            </div>
        </div>
    `;

    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeModal() {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Case Study Data
function getCaseStudyData(id) {
    const caseStudies = {
        'case-study-1': {
            title: 'Enterprise System Integration',
            description: 'Successfully integrated multiple legacy systems with modern cloud-based solutions, resulting in improved operational efficiency and reduced maintenance costs.',
            results: [
                '40% reduction in system maintenance costs',
                '60% faster data processing',
                '99.9% system uptime achieved',
                '50% reduction in manual data entry'
            ],
            technologies: ['AWS', 'Docker', 'Kubernetes', 'REST APIs', 'GraphQL']
        },
        'case-study-2': {
            title: 'E-commerce Platform Integration',
            description: 'Implemented seamless integration between e-commerce platform, inventory management, and payment processing systems.',
            results: [
                '35% increase in order processing speed',
                '25% reduction in inventory discrepancies',
                '45% improvement in customer satisfaction',
                '30% increase in sales conversion rate'
            ],
            technologies: ['Shopify', 'Stripe', 'MongoDB', 'Node.js', 'Redis']
        },
        'case-study-3': {
            title: 'Healthcare Systems Integration',
            description: 'Developed and implemented a comprehensive healthcare data integration solution connecting multiple hospital systems.',
            results: [
                '70% reduction in data entry errors',
                '85% faster patient data access',
                '50% decrease in administrative overhead',
                '100% compliance with healthcare regulations'
            ],
            technologies: ['HL7', 'FHIR', 'HIPAA', 'Python', 'PostgreSQL']
        }
    };

    return caseStudies[id];
}

// Scroll Reveal Animation
function handleScrollReveal() {
    const windowHeight = window.innerHeight;
    const revealPoint = 150;

    scrollRevealElements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;

        if (elementTop < windowHeight - revealPoint) {
            element.classList.add('revealed');
        }
    });
}

// Parallax Effect
function handleParallax() {
    const parallaxElements = document.querySelectorAll('.parallax');
    const scrolled = window.pageYOffset;

    parallaxElements.forEach(element => {
        const speed = element.dataset.speed || 0.5;
        element.style.transform = `translateY(${scrolled * speed}px)`;
    });
}

// Smooth Scroll
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    // Initialize smooth scroll
    initSmoothScroll();

    // Add click event listeners to case study buttons
    caseStudyButtons.forEach(button => {
        button.addEventListener('click', () => {
            const caseStudyId = button.dataset.caseStudy;
            openModal(caseStudyId);
        });
    });

    // Close modal when clicking the close button or outside the modal
    closeModalBtn.addEventListener('click', closeModal);
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modal.style.display === 'block') {
            closeModal();
        }
    });

    // Initialize scroll reveal
    handleScrollReveal();
    window.addEventListener('scroll', handleScrollReveal);

    // Initialize parallax effect
    window.addEventListener('scroll', handleParallax);
});

// Intersection Observer for lazy loading images
const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            observer.unobserve(img);
        }
    });
});

document.querySelectorAll('img.lazy').forEach(img => {
    imageObserver.observe(img);
});

// Add loading animation to buttons
document.querySelectorAll('.systems-btn').forEach(button => {
    button.addEventListener('click', function() {
        this.classList.add('loading');
        setTimeout(() => {
            this.classList.remove('loading');
        }, 1000);
    });
}); 